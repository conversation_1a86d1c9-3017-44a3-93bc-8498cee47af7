// 全局样式文件

// 导入样式文件 - @use规则必须在文件开头
@use './mobile.scss';
@use './dialog-override.scss';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// 全局焦点样式重置 - 统一使用主题色
*:focus {
  outline: none;
}

// 统一的焦点样式 - 使用主题色
button:focus,
input:focus,
textarea:focus,
select:focus,
[tabindex]:focus,
.el-button:focus,
.el-input__wrapper.is-focus,
.el-textarea__inner:focus,
.el-select:focus .el-input__wrapper,
.el-switch:focus .el-switch__core {
  box-shadow: 0 0 0 3px var(--box-shadow-focus) !important;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden; // 防止整体页面滚动
}

#app {
  height: 100%;
  overflow: hidden; // 防止应用容器滚动
}

// 自定义CSS变量 - 统一主题配色系统（与Element Plus保持一致）
:root {
  // 主题色 - 蓝色系高端配色（与Element Plus主色调一致）
  --primary-color: #3B82F6;
  --primary-color-rgb: 59, 130, 246; // RGB格式，用于rgba()
  --primary-light: #60A5FA;
  --primary-lighter: #93C5FD;
  --primary-lightest: #DBEAFE;
  --primary-dark: #2563EB;
  --primary-darker: #1D4ED8;

  // Element Plus 主色调映射
  --el-color-primary: #3B82F6;
  --el-color-primary-light-3: #60A5FA;
  --el-color-primary-light-5: #93C5FD;
  --el-color-primary-light-7: #BFDBFE;
  --el-color-primary-light-8: #DBEAFE;
  --el-color-primary-light-9: #EFF6FF;
  --el-color-primary-dark-2: #2563EB;

  // 功能色（与Element Plus保持一致）
  --success-color: #10B981;
  --success-light: #34D399;
  --success-lighter: #6EE7B7;
  --success-lightest: #ECFDF5;
  --success-dark: #059669;
  --warning-color: #F59E0B;
  --warning-light: #FBBF24;
  --warning-lighter: #FDE68A;
  --warning-lightest: #FFFBEB;
  --warning-dark: #D97706;
  --danger-color: #EF4444;
  --danger-light: #F87171;
  --danger-lighter: #FECACA;
  --danger-lightest: #FEF2F2;
  --danger-dark: #DC2626;
  --info-color: #6B7280;
  --info-light: #9CA3AF;
  --info-lighter: #E5E7EB;
  --info-lightest: #F9FAFB;
  --info-dark: #4B5563;

  // Element Plus 功能色映射
  --el-color-success: #10B981;
  --el-color-warning: #F59E0B;
  --el-color-danger: #EF4444;
  --el-color-error: #EF4444;
  --el-color-info: #6B7280;

  // 背景色 - 现代简洁
  --bg-color: #FFFFFF;
  --bg-color-page: #F8FAFC;
  --bg-color-table-header:#F8FAFC;
  --bg-color-overlay: #FFFFFF;
  --bg-color-sidebar: #1E293B;
  --bg-color-header: #FFFFFF;
  --bg-color-card: #FFFFFF;
  --bg-color-hover: #F1F5F9;

  // 文字色 - 层次分明
  --text-color-primary: #1F2937;
  --text-color-regular: #374151;
  --text-color-secondary: #6B7280;
  --text-color-placeholder: #9CA3AF;
  --text-color-disabled: #D1D5DB;
  --text-color-white: #FFFFFF;
  --text-color-inverse: #E2E8F0;
  --text-color-light: #F3F4F6;
  --text-color-inverse: #FFFFFF;

  // 菜单项颜色（图标+标题）- 浅色模式
  --menu-item-color: #E2E8F0;
  --menu-item-color-hover: #FFFFFF;
  --menu-item-color-active: #FFFFFF;

  // 菜单图标专用颜色 - 浅色模式
  --menu-icon-color: #E2E8F0;
  --menu-icon-color-hover: #FFFFFF;
  --menu-icon-color-active: #FFFFFF;

  // Tooltip 专用颜色 - 明亮主题
  --tooltip-bg-color: #1E293B;
  --tooltip-text-color: #FFFFFF;
  --tooltip-border-color: #334155;

  // 表格专用颜色 - 明亮主题
  --bg-color-table-header: #F8FAFC;

  // 边框色 - 精致细腻
  --border-color: #E5E7EB;
  --border-color-light: #F3F4F6;
  --border-color-lighter: #F9FAFB;
  --border-color-extra-light: #FAFBFC;
  --border-color-dark: #D1D5DB;
  --border-color-darker: #9CA3AF;

  // 阴影 - 立体层次
  --box-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --box-shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.2);

  // 布局相关
  --header-height: 64px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --content-padding: 24px;

  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-full: 50%;

  // 过渡动画
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  // 字体
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  // 遮罩层
  --overlay-color: rgba(0, 0, 0, 0.5);
}

// 深色主题 - 完整的深色主题变量覆盖
[data-theme="dark"] {
  // 背景色
  --bg-color: #1F2937;
  --bg-color-page: #111827;
  --bg-color-table-header:#374151;
  --bg-color-overlay: #1F2937;
  --bg-color-sidebar: #111827;
  --bg-color-header: #111827;
  --bg-color-card: #374151;
  --bg-color-hover: #4B5563;

  // 文字色
  --text-color-primary: #F9FAFB;
  --text-color-regular: #E5E7EB;
  --text-color-secondary: #D1D5DB;
  --text-color-placeholder: #9CA3AF;
  --text-color-disabled: #6B7280;
  --text-color-light: #374151;
  --text-color-inverse: #1F2937;

  // 菜单项颜色（图标+标题）- 深色模式，更亮更清晰
  --menu-item-color: #F1F5F9;
  --menu-item-color-hover: #FFFFFF;
  --menu-item-color-active: #FFFFFF;

  // Tooltip 专用颜色 - 暗黑主题
  --tooltip-bg-color: #374151;
  --tooltip-text-color: #F9FAFB;
  --tooltip-border-color: #4B5563;

  // 表格专用颜色 - 暗黑主题
  --bg-color-table-header: #1F2937;

  // 边框色
  --border-color: #4B5563;
  --border-color-light: #374151;
  --border-color-lighter: #374151;
  --border-color-extra-light: #1F2937;
  --border-color-dark: #6B7280;
  --border-color-darker: #9CA3AF;

  // 阴影 - 深色主题下的阴影
  --box-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --box-shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.3);

  // Element Plus 深色主题变量强制覆盖
  --el-bg-color: #1F2937;
  --el-bg-color-page: #111827;
  --el-bg-color-overlay: #1F2937;
  --el-text-color-primary: #F9FAFB;
  --el-text-color-regular: #E5E7EB;
  --el-text-color-secondary: #D1D5DB;
  --el-text-color-placeholder: #9CA3AF;
  --el-text-color-disabled: #6B7280;
  --el-border-color: #4B5563;
  --el-border-color-light: #374151;
  --el-border-color-lighter: #374151;
  --el-border-color-extra-light: #1F2937;
  --el-border-color-dark: #6B7280;
  --el-border-color-darker: #9CA3AF;
  --el-fill-color: #374151;
  --el-fill-color-light: #4B5563;
  --el-fill-color-lighter: #374151;
  --el-fill-color-extra-light: #1F2937;
  --el-fill-color-blank: transparent;
  --el-fill-color-dark: #6B7280;

  // 深色主题下的主色调保持不变
  --el-color-primary: #3B82F6;
  --el-color-primary-light-3: #60A5FA;
  --el-color-primary-light-5: #93C5FD;
  --el-color-success: #10B981;
  --el-color-warning: #F59E0B;
  --el-color-danger: #EF4444;
  --el-color-error: #EF4444;
  --el-color-info: #6B7280;

  // 深色主题下的功能色浅色版本
  --success-lightest: rgba(16, 185, 129, 0.1);
  --warning-lightest: rgba(245, 158, 11, 0.1);
  --danger-lightest: rgba(239, 68, 68, 0.1);
  --info-lightest: rgba(107, 114, 128, 0.1);
  --primary-lightest: rgba(59, 130, 246, 0.1);

  // 深色主题下的遮罩层
  --overlay-color: rgba(31, 41, 55, 0.8);
}

// Element Plus 菜单弹出层样式 - 简洁设计，与菜单保持一致
.el-menu--popup,
.el-menu--popup-container,
.el-popper[data-popper-placement^="left"] {
  background: var(--bg-color-sidebar) !important;
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  padding: 2px !important;
  min-width: 160px !important;

  .el-menu-item {
    background: transparent !important;
    color: var(--menu-item-color) !important;
    height: 44px !important;
    line-height: 44px !important;
    padding: 0 16px !important;
    margin: 2px 4px !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: rgba(var(--primary-color-rgb), 0.1) !important;
      color: var(--menu-item-color-hover) !important;
    }

    &.is-active {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      color: var(--text-color-white) !important;
      box-shadow: var(--box-shadow-lg);
    }
  }
}

// Element Plus 菜单 Tooltip 主题适配
.el-popper.is-dark.el-tooltip {
  background: var(--tooltip-bg-color) !important;
  border: 1px solid var(--tooltip-border-color) !important;
  color: var(--tooltip-text-color) !important;
  box-shadow: var(--box-shadow-lg) !important;

  // Tooltip 内容文字颜色
  .menu-title {
    color: var(--tooltip-text-color) !important;
  }

  // Tooltip 箭头样式
  .el-popper__arrow::before {
    background: var(--tooltip-bg-color) !important;
    border-color: var(--tooltip-border-color) !important;
  }
}

// 确保所有可能的 Tooltip 变体都被覆盖
.el-popper[role="tooltip"].is-dark,
.el-tooltip.is-dark {
  background: var(--tooltip-bg-color) !important;
  border: 1px solid var(--tooltip-border-color) !important;
  color: var(--tooltip-text-color) !important;
  box-shadow: var(--box-shadow-lg) !important;

  .el-popper__arrow::before {
    background: var(--tooltip-bg-color) !important;
    border-color: var(--tooltip-border-color) !important;
  }
}

// 全局表格样式优化 - 统一所有列表组件的表格样式
.el-table {
  border-radius: 8px;
  overflow: hidden;

  // 表头样式
  .el-table__header-wrapper {
    .el-table__header {
      th {
        border-bottom: 2px solid var(--border-color);
        font-weight: 600;
        font-size: 14px;
        background: var(--bg-color-table-header);

        // 支持表头居中对齐
        &.is-center .cell {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  // 表体样式
  .el-table__body-wrapper {
    .el-table__row {
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--bg-color-page) !important;
        transform: translateY(-1px);
        box-shadow: var(--box-shadow-base);
      }

      td {
        border-bottom: 1px solid var(--border-color-light);
        vertical-align: middle;

        .cell {
          display: flex;
          align-items: center;
          min-height: 32px;
        }

        // 支持居中对齐的列
        &.is-center .cell {
          justify-content: center;
        }

        // 第一列特殊处理
        &:first-child .cell {
          align-items: center;

          .el-table__expand-icon {
            margin-right: 8px;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  // 空状态占位符
  .el-table__placeholder {
    margin-right: 8px;
  }

  // 树形表格展开图标样式
  .el-table__expand-icon {
    color: var(--primary-color);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 8px;
    transition: transform 0.2s ease;

    &.el-table__expand-icon--expanded {
      transform: rotate(90deg);
    }
  }

  // 树形缩进线条
  .el-table__indent {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: -16px;
      bottom: 16px;
      width: 1px;
      background: var(--border-color);
    }
  }

  // 选择框列样式
  .el-table-column--selection {
    .cell {
      justify-content: center;
    }
  }

  // 操作列样式
  .el-table__fixed-right {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  }

  // 强制居中对齐样式
  .el-table__header th.is-center,
  .el-table__body td.is-center {
    text-align: center !important;

    .cell {
      justify-content: center !important;
      text-align: center !important;
    }
  }
}

// 全局搜索操作区样式 - 统一所有列表页面的搜索区域
.search-section {
  background: var(--bg-color-card);
  padding: 20px;
  border-radius: 8px;
  box-shadow: var(--box-shadow-base);
  margin-bottom: 20px;

  .search-form {
    margin-bottom: 16px;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: start;
  }
}


// 全局表格区域样式 - 统一所有列表页面的表格容器
.table-section {
  background: var(--bg-color-card);
  padding: 0;
  border-radius: 8px;
  box-shadow: var(--box-shadow-base);
  overflow: hidden;
}


// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

// 间距工具类
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }

// 响应式断点
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-color-light);
}

// 动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// Element Plus 全局样式增强
.el-popper {
  border-radius: var(--border-radius-lg) !important;
  box-shadow: var(--box-shadow-lg) !important;
}

.el-overlay {
  backdrop-filter: blur(4px);
}

// Element Plus 按钮主题色增强
.el-button--primary {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);

  &:hover {
    background-color: var(--el-color-primary-light-3);
    border-color: var(--el-color-primary-light-3);
  }

  &:active {
    background-color: var(--el-color-primary-dark-2);
    border-color: var(--el-color-primary-dark-2);
  }
}

// Element Plus 按钮间距优化 - 移动端取消默认的 margin-left
@media (max-width: 768px) {
  .action-buttons,.dialog-footer {
    .el-button + .el-button {
      margin-left: 0 !important;
    }
  }
  
}

// Element Plus 链接主题色
.el-link--primary {
  color: var(--el-color-primary);

  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

// Element Plus 表单组件焦点样式
.el-input__wrapper.is-focus,
.el-textarea__inner:focus,
.el-select:focus .el-input__wrapper {
  border-color: var(--el-color-primary) !important;
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}

// Element Plus 开关组件主题色
.el-switch.is-checked .el-switch__core {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

// Element Plus 复选框主题色
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

// Element Plus 单选框主题色
.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

// Element Plus 滑块主题色
.el-slider__button {
  border-color: var(--el-color-primary);
}

.el-slider__bar {
  background-color: var(--el-color-primary);
}

// Element Plus 结果组件深色主题适配
.el-result {
  background: var(--bg-color-card);

  .el-result__title {
    color: var(--text-color-primary);
  }

  .el-result__subtitle {
    color: var(--text-color-secondary);
  }
}

// Element Plus 对话框深色主题适配
.el-dialog {
  background: var(--bg-color-card);

  .el-dialog__header {
    border-bottom: 1px solid var(--border-color);
  }

  .el-dialog__title {
    color: var(--text-color-primary);
  }
}

// Element Plus 表格加载状态 - 明亮模式
.el-table {
  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.8) !important;

    .el-loading-spinner {
      .path {
        stroke: var(--primary-color) !important;
      }

      .el-loading-text {
        color: var(--text-color-primary) !important;
        font-weight: 500;
      }

      .circular {
        stroke: var(--primary-color) !important;
      }
    }
  }
}

// Element Plus 表格加载状态 - 深色模式
[data-theme="dark"] .el-table {
  .el-loading-mask {
    background-color: var(--overlay-color) !important;

    .el-loading-spinner {
      .path {
        stroke: var(--primary-color) !important;
      }

      .el-loading-text {
        color: var(--text-color-primary) !important;
        font-weight: 500;
      }

      .circular {
        stroke: var(--primary-color) !important;
      }
    }
  }

  // 表格空状态
  .el-table__empty-block {
    background: var(--bg-color-card) !important;

    .el-table__empty-text {
      color: var(--text-color-secondary) !important;
    }
  }

  // 表格头部在深色主题下的样式
  .el-table__header-wrapper {
    background: var(--bg-color-card) !important;
  }

  // 表格体在深色主题下的样式
  .el-table__body-wrapper {
    background: var(--bg-color-card) !important;
  }
}

// Element Plus 全局加载组件 - 明亮模式
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8) !important;

  &.is-fullscreen {
    background-color: rgba(255, 255, 255, 0.9) !important;
  }

  .el-loading-spinner {
    .path {
      stroke: var(--primary-color) !important;
    }

    .el-loading-text {
      color: var(--text-color-primary) !important;
      font-weight: 500;
    }

    .circular {
      stroke: var(--primary-color) !important;
    }
  }
}

// Element Plus 全局加载组件 - 深色模式
[data-theme="dark"] .el-loading-mask {
  background-color: var(--overlay-color) !important;

  &.is-fullscreen {
    background-color: var(--overlay-color) !important;
  }

  .el-loading-spinner {
    .path {
      stroke: var(--primary-color) !important;
    }

    .el-loading-text {
      color: var(--text-color-primary) !important;
      font-weight: 500;
    }

    .circular {
      stroke: var(--primary-color) !important;
    }
  }
}

// Element Plus 其他组件的加载状态适配
.el-button.is-loading {
  .el-icon {
    color: var(--text-color-white) !important;
  }
}

// Element Plus 骨架屏 - 明亮模式
.el-skeleton {
  .el-skeleton__item {
    background: var(--border-color-light) !important;
  }
}

// Element Plus 骨架屏 - 深色模式
[data-theme="dark"] .el-skeleton {
  .el-skeleton__item {
    background: var(--border-color-dark) !important;
  }
}

// Element Plus 空状态适配
.el-empty {
  .el-empty__description {
    color: var(--text-color-secondary) !important;
  }
}

// Element Plus 分页组件样式优化
.el-pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-wrap: wrap;
  gap: 8px;

  .el-pagination__total {
    color: var(--text-color-regular) !important;
    font-size: 14px;
    margin-right: 16px;
  }

  .el-pagination__sizes {
    margin-right: 16px;

    .el-select {
      .el-select__wrapper {
        min-height: 32px;
        border-radius: 6px;
      }
    }
  }

  .el-pagination__jump {
    color: var(--text-color-regular) !important;
    font-size: 14px;
    margin-left: 16px;

    .el-pagination__editor {
      .el-input__wrapper {
        min-height: 32px;
        border-radius: 6px;
        width: 50px;
      }
    }
  }

  // 分页按钮样式
  .btn-prev,
  .btn-next {
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background: var(--bg-color-container);
    color: var(--text-color-regular);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;

    &:hover:not(.is-disabled) {
      border-color: var(--primary-color);
      color: var(--primary-color);
    }

    &.is-disabled {
      color: var(--text-color-disabled);
      cursor: not-allowed;
    }
  }

  // 页码按钮样式
  .el-pager {
    display: flex;
    align-items: center;
    gap: 4px;

    li {
      border-radius: 6px;
      border: 1px solid var(--border-color);
      background: var(--bg-color-container);
      color: var(--text-color-regular);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover:not(.is-active) {
        border-color: var(--primary-color);
        color: var(--primary-color);
      }

      &.is-active {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        color: #ffffff !important;
      }

      &.more {
        border: none;
        background: transparent;

        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }
}

/* ========== 页面容器统一样式 ========== */

// 通用页面容器基类 - 所有页面都应该使用这个基类
.page-container {
  background: var(--bg-color-page);
  // min-height: calc(100vh - 60px);
}

// 管理页面容器 - 继承通用页面容器
.management-page {
  @extend .page-container;
}

// 内容包装器 - Layout组件中使用
.content-wrapper {
  @extend .page-container;
  padding: 24px;
  // min-height: calc(100vh - var(--header-height) - 40px - 48px); // 减去标签页高度40px
}

// 页面标题区域
.page-header {
  margin-bottom: 20px;

  .page-title {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color-primary);
  }

  .page-description {
    margin: 0;
    color: var(--text-color-secondary);
    font-size: 14px;
  }
}

// 搜索和操作区域（继承全局 .search-section 样式）
.search-section {
  .search-form {
    margin-bottom: 16px;
  }

  .action-buttons {
    display: flex;
    gap: 12px;
  }
}

// 表格区域（继承全局 .table-section 样式）

// 管理页面响应式设计（以用户类型管理页面为标准）
@media (max-width: 768px) {
  .management-page {
    padding: 16px;
  }

  .search-form :deep(.el-form) {
    flex-direction: column;
  }

  .search-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .pagination-container {
    justify-content: center;

    .el-pagination {
      justify-content: center;
      gap: 4px;

      .el-pagination__total {
        order: -1;
        width: 100%;
        text-align: center;
        margin: 0 0 12px 0;
      }

      .el-pagination__sizes {
        margin: 0 8px 0 0;
      }

      .el-pagination__jump {
        margin: 0 0 0 8px;

        .el-pagination__editor {
          .el-input__wrapper {
            width: 40px;
          }
        }
      }

      .btn-prev,
      .btn-next {
        min-width: 28px;
        height: 28px;
      }

      .el-pager li {
        min-width: 28px;
        height: 28px;
        font-size: 13px;
      }
    }
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .pagination-container {
    .el-pagination {
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }

      .el-pagination__total {
        font-size: 13px;
      }
    }
  }
}

// Element Plus 消息提示深色主题适配
.el-message {
  background: var(--bg-color-card) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--box-shadow-lg) !important;

  .el-message__content {
    color: var(--text-color-primary) !important;
  }
}

// Element Plus 通知深色主题适配
.el-notification {
  background: var(--bg-color-card) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--box-shadow-xl) !important;

  .el-notification__title {
    color: var(--text-color-primary) !important;
  }

  .el-notification__content {
    color: var(--text-color-regular) !important;
  }
}

// Element Plus 抽屉深色主题适配
.el-drawer {
  background: var(--bg-color-card) !important;

  .el-drawer__header {
    border-bottom: 1px solid var(--border-color) !important;

    .el-drawer__title {
      color: var(--text-color-primary) !important;
    }
  }
}

// 全局过渡动画增强
.slide-fade-enter-active {
  transition: all var(--transition-normal);
}

.slide-fade-leave-active {
  transition: all var(--transition-fast);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

// 深色主题下的滚动条
[data-theme="dark"] {
  ::-webkit-scrollbar-track {
    background: var(--bg-color-page);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-color);

    &:hover {
      background: var(--border-color-dark);
    }
  }
}

// 移动端滚动优化
@media (max-width: 767px) {
  // 防止整体页面滚动
  html, body {
    position: fixed;
    width: 100%;
    height: 100%;
    height: calc(var(--vh, 1vh) * 100); /* 使用JavaScript计算的动态高度 */
    height: 100dvh; /* 现代浏览器的动态视口高度 */
    overflow: hidden;
  }

  #app {
    position: fixed;
    width: 100%;
    height: 100%;
    height: calc(var(--vh, 1vh) * 100); /* 使用JavaScript计算的动态高度 */
    height: 100dvh; /* 现代浏览器的动态视口高度 */
    overflow: hidden;
  }

  // 优化移动端滚动性能
  .layout-content {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0); // 启用硬件加速
  }

  // 防止移动端橡皮筋效果
  .layout-content {
    overscroll-behavior: contain;
  }

  // 处理移动端底部安全区域
  .content-wrapper {
    padding-bottom: calc(12px + env(safe-area-inset-bottom, 0px));
  }
}

// 移动端横屏优化
@media (max-width: 1024px) and (orientation: landscape) {
  html, body {
    position: fixed;
    width: 100%;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    height: 100dvh;
    overflow: hidden;
  }

  #app {
    position: fixed;
    width: 100%;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    height: 100dvh;
    overflow: hidden;
  }

  // 横屏时优化滚动性能
  .layout-content {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    overscroll-behavior: contain;
  }

  // 横屏时减少底部内边距
  .content-wrapper {
    padding-bottom: calc(8px + env(safe-area-inset-bottom, 0px));
  }

  // 横屏时调整CSS变量
  :root {
    --header-height: 50px; // 横屏时减少头部高度
  }
}


@media (max-width: 768px) {
    .el-date-range-picker .el-picker-panel__body {
        min-width: 100%;
    }     
    .el-date-range-picker__content{        
        width: 100% !important;    
    }    
    .el-date-range-picker{        
        width: 100% !important;    
    }
}

