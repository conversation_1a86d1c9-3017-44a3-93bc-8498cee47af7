import request from '@/utils/request'

/**
 * 获取当前用户菜单列表
 * @returns {Promise<Array<Object>>} 菜单树数据
 */
export const getUserMenus = () => {
  return request({
    url: '/route/routers',
    method: 'get'
  })
}

/**
 * 获取菜单列表（管理端）
 * @param {Object} params 查询参数
 * @param {string} params.menuName 菜单名称（模糊查询）
 * @param {number} params.menuType 菜单类型：1-目录，2-菜单，3-按钮
 * @param {number} params.status 状态：0-禁用，1-启用
 * @returns {Promise<Array<Object>>} 菜单树数据
 */
export const getMenus = (params = {}) => {
  return request({
    url: '/menu/getMenus',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取菜单详情
 * @param {string} id 菜单ID
 * @returns {Promise<Object>} 菜单详情
 */
export const getMenuById = (id) => {
  return request({
    url: `/menu/${id}`,
    method: 'get'
  })
}

/**
 * 创建菜单
 * @param {Object} data 菜单数据
 * @param {string} data.parentId 父菜单ID，顶级菜单时为空
 * @param {string} data.menuName 菜单名称
 * @param {number} data.menuType 菜单类型：1-目录，2-菜单，3-按钮
 * @param {string} data.menuCode 菜单编码
 * @param {string} data.routePath 路由路径
 * @param {string} data.componentPath 组件路径
 * @param {string} data.icon 图标名称
 * @param {number} data.sortOrder 排序号
 * @param {number} data.visible 是否可见：0-隐藏，1-显示
 * @param {number} data.status 状态：0-禁用，1-启用
 * @param {string} data.permission 权限标识
 * @param {number} data.externalLink 是否外部链接：0-否，1-是
 * @param {string} data.openMode 打开方式：_self-当前窗口，_blank-新窗口
 * @param {string} data.routeParams 路由参数，JSON格式
 * @returns {Promise<Object>} 创建结果
 */
export const createMenu = (data) => {
  return request({
    url: '/menu',
    method: 'post',
    data
  })
}

/**
 * 更新菜单
 * @param {Object} data 菜单数据
 * @param {string} data.id 菜单ID
 * @param {string} data.parentId 父菜单ID，顶级菜单时为空
 * @param {string} data.menuName 菜单名称
 * @param {number} data.menuType 菜单类型：1-目录，2-菜单，3-按钮
 * @param {string} data.menuCode 菜单编码
 * @param {string} data.routePath 路由路径
 * @param {string} data.componentPath 组件路径
 * @param {string} data.icon 图标名称
 * @param {number} data.sortOrder 排序号
 * @param {number} data.visible 是否可见：0-隐藏，1-显示
 * @param {number} data.status 状态：0-禁用，1-启用
 * @param {string} data.permission 权限标识
 * @param {number} data.externalLink 是否外部链接：0-否，1-是
 * @param {string} data.openMode 打开方式：_self-当前窗口，_blank-新窗口
 * @param {string} data.routeParams 路由参数，JSON格式
 * @returns {Promise<Object>} 更新结果
 */
export const updateMenu = (data) => {
  return request({
    url: '/menu',
    method: 'put',
    data
  })
}

/**
 * 删除菜单
 * @param {string} id 菜单ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteMenu = (id) => {
  return request({
    url: `/menu/${id}`,
    method: 'delete'
  })
}

/**
 * 批量更新菜单状态
 * @param {Object} data 批量更新数据
 * @param {Array<string>} data.menuIds 菜单ID列表
 * @param {number} data.status 要设置的状态：0-禁用，1-启用
 * @returns {Promise<Object>} 批量更新结果
 */
export const batchUpdateStatus = (data) => {
  // 将菜单ID数组转换为逗号分隔的字符串
  const menuIds = Array.isArray(data.menuIds) ? data.menuIds.join(',') : data.menuIds

  return request({
    url: '/menu/batch-status',
    method: 'put',
    params: {
      menuIds: menuIds,
      status: data.status
    }
  })
}
