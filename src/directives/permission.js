import { hasPermission, hasAnyPermission, hasAllPermissions } from '@/utils/permission'

/**
 * 权限指令
 * 用法：
 * v-permission="'system:user:view'" - 检查单个权限
 * v-permission="['system:user:view', 'system:user:edit']" - 检查多个权限（AND逻辑）
 * v-permission:any="['system:user:view', 'system:user:edit']" - 检查多个权限（OR逻辑）
 */
const permission = {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  }
}

/**
 * 检查权限并控制元素显示
 * @param {HTMLElement} el DOM元素
 * @param {Object} binding 指令绑定对象
 */
function checkPermission(el, binding) {
  const { value, arg } = binding
  let hasAuth = false

  if (value === undefined || value === null) {
    // 没有权限要求，默认显示
    hasAuth = true
  } else if (typeof value === 'string') {
    // 单个权限字符串
    hasAuth = hasPermission(value)
  } else if (Array.isArray(value)) {
    // 权限数组
    if (arg === 'any') {
      // OR逻辑：有任意一个权限即可
      hasAuth = hasAnyPermission(value)
    } else {
      // AND逻辑：需要所有权限（默认）
      hasAuth = hasAllPermissions(value)
    }
  }

  // 控制元素显示/隐藏
  if (hasAuth) {
    el.style.display = ''
  } else {
    el.style.display = 'none'
  }
}

export default permission
