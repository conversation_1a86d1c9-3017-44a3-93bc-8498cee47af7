<template>
  <div class="session-expired-container">
    <div class="session-expired-content">
      <div class="icon-wrapper">
        <el-icon class="expired-icon" :size="80">
          <WarningFilled />
        </el-icon>
      </div>
      
      <h1 class="title">{{ pageTitle }}</h1>
      <p class="description">
        {{ pageDescription }}
      </p>
      
      <div class="countdown-wrapper">
        <p class="countdown-text">
          {{ countdown }} 秒后自动跳转到登录页面
        </p>
        <el-progress 
          :percentage="progressPercentage" 
          :show-text="false"
          :stroke-width="4"
          :color="'var(--primary-color)'"
        />
      </div>
      
      <div class="action-buttons">
        <el-button 
          type="primary" 
          size="large"
          @click="goToLogin"
          class="login-button"
        >
          立即登录
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { WarningFilled } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 响应式数据
const countdown = ref(10)
const progressPercentage = ref(100)
let timer = null

// 根据来源显示不同的标题和描述
const pageTitle = computed(() => {
  const reason = route.query.reason
  return reason === 'unauthorized' ? '需要登录' : '登录已失效'
})

const pageDescription = computed(() => {
  const reason = route.query.reason
  return reason === 'unauthorized'
    ? '您需要登录后才能访问此页面，请先进行登录'
    : '您的登录状态已过期，请重新登录以继续使用系统'
})

// 计算进度百分比
const updateProgress = () => {
  progressPercentage.value = (countdown.value / 5) * 100
}

// 开始倒计时
const startCountdown = () => {
  timer = setInterval(() => {
    countdown.value--
    updateProgress()
    
    if (countdown.value <= 0) {
      clearInterval(timer)
      goToLogin()
    }
  }, 1000)
}

// 跳转到登录页
const goToLogin = () => {
  if (timer) {
    clearInterval(timer)
  }
  router.push('/login')
}

// 组件挂载时开始倒计时和设置页面标题
onMounted(() => {
  startCountdown()
  // 根据不同情况设置页面标题
  document.title = `${pageTitle.value} - aKey`
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.session-expired-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: 20px;
}

.session-expired-content {
  background: var(--bg-color-overlay);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: var(--box-shadow-xl);
  max-width: 500px;
  width: 100%;
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-wrapper {
  margin-bottom: 30px;
}

.expired-icon {
  color: var(--danger-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.title {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin-bottom: 16px;
  margin-top: 0;
}

.description {
  font-size: 16px;
  color: var(--text-color-secondary);
  margin-bottom: 40px;
  line-height: 1.6;
}

.countdown-wrapper {
  margin-bottom: 40px;
}

.countdown-text {
  font-size: 14px;
  color: var(--text-color-regular);
  margin-bottom: 16px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.login-button {
  min-width: 120px;
  height: 44px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-lg);
}



/* 响应式设计 */
@media (max-width: 768px) {
  .session-expired-content {
    padding: 40px 20px;
    margin: 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .login-button,
  .home-button {
    width: 100%;
    max-width: 200px;
  }
}
</style>
