<template>
  <div class="iframe-container">
    <!-- Element Plus Loading 覆盖层 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="loading-spinner">
        <Loading />
      </el-icon>
      <p class="loading-text">{{ loadingText }}</p>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <el-icon class="error-icon">
        <Warning />
      </el-icon>
      <h3 class="error-title">页面加载失败</h3>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <el-button type="primary" @click="reload">
          <el-icon><Refresh /></el-icon>
          重新加载
        </el-button>
        <el-button @click="openInNewWindow">
          <el-icon><Link /></el-icon>
          在新窗口打开
        </el-button>
      </div>
    </div>

    <!-- iframe 内容 -->
    <iframe
      v-if="!error"
      ref="iframeRef"
      :src="finalUrl"
      class="iframe-content"
      :class="{ 'iframe-loading': loading }"
      frameborder="0"
      sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-top-navigation"
      @load="handleLoad"
      @error="handleError"
    ></iframe>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Warning, Refresh, Link, Loading } from '@element-plus/icons-vue'

export default {
  name: 'IframeView',
  components: {
    Warning,
    Refresh,
    Link,
    Loading
  },
  setup() {
    const route = useRoute()
    const iframeRef = ref(null)
    const loading = ref(true)
    const error = ref(false)
    const errorMessage = ref('')
    const loadTimeout = ref(null)
    const loadingText = ref('正在加载页面...')
  
    // 从查询参数或路由meta获取 URL 和标题
    const url = computed(() => {
      console.log(route)
      // 优先从查询参数获取，如果没有则从路由meta获取
      return route.query.url || route.meta?.externalUrl || ''
    })
    const title = computed(() => {
      // 优先从查询参数获取，如果没有则从路由meta获取
      return route.query.title || route.meta?.title || '外部页面'
    })

    // 处理 URL 格式
    const finalUrl = computed(() => {
      if (!url.value) return ''
      
      let processedUrl = url.value
      // 确保 URL 有协议前缀
      if (!processedUrl.startsWith('http://') && !processedUrl.startsWith('https://')) {
        processedUrl = 'https://' + processedUrl
      }
      
      return processedUrl
    })

    // 处理 iframe 加载完成
    const handleLoad = () => {
      loading.value = false
      error.value = false
      clearTimeout(loadTimeout.value)
      
      // 设置页面标题
      if (title.value) {
        document.title = `${title.value} - aKey`
      }
    }

    // 处理 iframe 加载错误
    const handleError = () => {
      loading.value = false
      error.value = true
      errorMessage.value = '无法加载此页面，可能是网络问题或该网站不允许在框架中显示。'
      clearTimeout(loadTimeout.value)
    }

    // 重新加载
    const reload = () => {
      loading.value = true
      loadingText.value = '正在重新加载页面...'
      error.value = false
      errorMessage.value = ''

      if (iframeRef.value) {
        iframeRef.value.src = finalUrl.value
      }

      // 设置超时
      loadTimeout.value = setTimeout(() => {
        if (loading.value) {
          handleError()
        }
      }, 30000) // 30秒超时
    }

    // 在新窗口打开
    const openInNewWindow = () => {
      if (finalUrl.value) {
        window.open(finalUrl.value, '_blank', 'noopener,noreferrer')
        ElMessage.success('已在新窗口打开页面')
      }
    }

    // 组件挂载时的处理
    onMounted(() => {
      if (!url.value) {
        error.value = true
        errorMessage.value = '缺少页面地址参数'
        loading.value = false
        return
      }

      // 设置加载超时
      loadTimeout.value = setTimeout(() => {
        if (loading.value) {
          handleError()
        }
      }, 30000) // 30秒超时
    })

    // 组件卸载时清理
    onUnmounted(() => {
      clearTimeout(loadTimeout.value)
    })

    return {
      iframeRef,
      loading,
      loadingText,
      error,
      errorMessage,
      finalUrl,
      title,
      handleLoad,
      handleError,
      reload,
      openInNewWindow
    }
  }
}
</script>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  height: calc(100vh - var(--header-height) - 40px - 48px - 48px); /* 减去头部、标签页和内边距 */
  min-height: 600px;
  background: var(--bg-color-page);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--box-shadow-base);
}

.iframe-content {
  width: 100%;
  height: 100%;
  border: none;
  background: #fff;
  transition: opacity 0.3s ease;
}

.iframe-loading {
  opacity: 0.3;
}

/* Element Plus 风格的 Loading 覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
  backdrop-filter: blur(2px);
}

[data-theme="dark"] .loading-overlay {
  background: rgba(31, 41, 55, 0.9);
}

.loading-spinner {
  font-size: 32px;
  color: var(--primary-color);
  animation: rotate 2s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  color: var(--text-color-regular);
  margin: 0;
  font-weight: 500;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  max-width: 400px;
  padding: 32px;
}

.error-icon {
  font-size: 64px;
  color: var(--color-warning);
  margin-bottom: 16px;
}

.error-title {
  font-size: 20px;
  color: var(--text-color-primary);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.error-message {
  font-size: 14px;
  color: var(--text-color-regular);
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .el-button {
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .iframe-container {
    height: calc(100vh - var(--header-height) - 40px - 48px - 24px);
    min-height: 400px;
  }
  
  .error-container {
    max-width: 300px;
    padding: 24px;
  }
  
  .error-icon {
    font-size: 48px;
  }
  
  .error-title {
    font-size: 18px;
  }
  
  .error-message {
    font-size: 13px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .iframe-content {
    background: var(--bg-color-page);
  }
}
</style>
