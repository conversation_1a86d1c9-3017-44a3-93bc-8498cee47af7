<template>
  <div class="icon-selector">
    <el-input
      v-model="selectedIcon"
      placeholder="请选择图标"
      readonly
      @click="dialogVisible = true"
      style="width: 100%"
    >
      <template #prefix>
        <svg-icon 
          v-if="selectedIcon" 
          :name="selectedIcon" 
          class="input-icon"
        />
      </template>
      <template #suffix>
        <el-icon class="cursor-pointer">
          <ArrowDown />
        </el-icon>
      </template>
    </el-input>

    <el-dialog
      v-model="dialogVisible"
      title="选择图标"
      width="800px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="icon-selector-content">
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索图标..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="icon-grid">
          <div
            v-for="iconName in filteredIcons"
            :key="iconName"
            class="icon-item"
            :class="{ active: selectedIcon === iconName }"
            @click="handleSelectIcon(iconName)"
          >
            <div class="icon-wrapper">
              <svg-icon :name="iconName" class="icon" />
            </div>
            <div class="icon-name">{{ iconName }}</div>
          </div>
        </div>

        <div v-if="filteredIcons.length === 0" class="empty-state">
          <el-empty description="未找到匹配的图标" />
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button @click="handleClear">清空</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ArrowDown, Search } from '@element-plus/icons-vue'
import { getAvailableIcons, searchIcons } from '@/utils/iconLoader'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = ref(false)
const searchKeyword = ref('')
const selectedIcon = ref(props.modelValue)

// 可用的图标列表（自动从 SVG 文件夹加载）
const availableIcons = ref([])

// 计算属性
const filteredIcons = computed(() => {
  return searchIcons(searchKeyword.value, availableIcons.value)
})

// 方法
const loadIcons = () => {
  try {
    availableIcons.value = getAvailableIcons()
  } catch (error) {
    console.warn('无法自动加载图标列表，使用默认图标:', error)
    // 如果自动加载失败，使用默认图标列表
    availableIcons.value = [
      'user', 'setting', 'menu', 'home', 'dashboard', 'edit', 'delete',
      'plus', 'minus', 'check', 'close', 'search', 'refresh', 'upload',
      'download', 'file', 'folder', 'document', 'image', 'chart',
      'table', 'form', 'button', 'lock', 'permission', 'email',
      'phone', 'calendar', 'clock', 'star', 'heart', 'tag'
    ]
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleSelectIcon = (iconName) => {
  selectedIcon.value = iconName
}

const handleConfirm = () => {
  emit('update:modelValue', selectedIcon.value)
  dialogVisible.value = false
}

const handleClear = () => {
  selectedIcon.value = ''
  emit('update:modelValue', '')
  dialogVisible.value = false
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedIcon.value = newValue
})

// 组件挂载时加载图标
onMounted(() => {
  loadIcons()
})
</script>

<style lang="scss" scoped>
.icon-selector {
  width: 100%;

  .input-icon {
    width: 16px;
    height: 16px;
    color: var(--text-color-secondary);
  }

  .cursor-pointer {
    cursor: pointer;
  }
}

.icon-selector-content {
  .search-section {
    margin-bottom: 20px;
  }

  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 8px;
    max-height: 450px;
    overflow-y: auto;
    padding: 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-color-page);

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 8px 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: var(--bg-color-card);
      border: 2px solid transparent;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, var(--primary-lightest), var(--primary-lighter));
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        background: var(--bg-color-hover);
        border-color: var(--border-color-light);
        transform: translateY(-3px);
        box-shadow: var(--box-shadow-lg);

        &::before {
          opacity: 1;
        }
      }

      &.active {
        background: var(--primary-lightest);
        box-shadow: var(--box-shadow-focus);
        transform: translateY(-2px);

        &::before {
          opacity: 1;
          background: linear-gradient(135deg, var(--primary-lightest), var(--primary-lighter));
        }
      }

      .icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
        border-radius: 8px;
        background: #f8fafc;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;

        .icon {
          width: 22px;
          height: 22px;
          color: var(--text-color-regular);
          transition: all 0.3s ease;
        }
      }

      .icon-name {
        font-size: 11px;
        color: var(--text-color-secondary);
        text-align: center;
        word-break: break-all;
        line-height: 1.3;
        font-weight: 500;
        position: relative;
        z-index: 1;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:hover {
        .icon-wrapper {
          
          transform: scale(1.05);
        }

        .icon {
          color: var(--text-color-primary);
        }

        .icon-name {
          color: var(--text-color-regular);
        }
      }

      &.active {
        .icon-wrapper {
          background: var(--primary-lightest);
          transform: scale(1.1);
          box-shadow: var(--box-shadow-lg);
        }

        .icon {
          color: var(--primary-color);
        }

        .icon-name {
          color: var(--primary-color);
          font-weight: 600;
        }
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
