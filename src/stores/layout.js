import { defineStore } from 'pinia'
import { ref, computed, onMounted, onUnmounted } from 'vue'

export const useLayoutStore = defineStore('layout', () => {
  // 屏幕宽度
  const screenWidth = ref(window.innerWidth)

  // 设备类型检测
  const isMobile = computed(() => screenWidth.value < 768)
  const isTablet = computed(() => screenWidth.value >= 768 && screenWidth.value < 1024)
  const isDesktop = computed(() => screenWidth.value >= 1024)

  // 侧边栏状态
  const isCollapsed = ref(false)
  const isMobileSidebarVisible = ref(false) // 移动端侧边栏显示状态

  // 计算实际的折叠状态（用于Element Plus菜单组件）
  const actualCollapsed = computed(() => {
    if (isMobile.value) {
      return false // 移动端不使用折叠模式，而是显示/隐藏
    }
    if (isTablet.value) {
      return true // 平板端默认折叠
    }
    return isCollapsed.value // 桌面端根据用户设置
  })

  // 侧边栏宽度配置
  const sidebarWidth = computed(() => {
    if (isMobile.value) {
      return '280px' // 移动端固定宽度
    }
    return actualCollapsed.value ? '64px' : '240px'
  })

  // 响应式处理
  const handleResize = () => {
    const oldScreenWidth = screenWidth.value
    screenWidth.value = window.innerWidth

    // 检测设备类型变化
    const wasDesktop = oldScreenWidth >= 1024
    const wasTablet = oldScreenWidth >= 768 && oldScreenWidth < 1024
    const wasMobile = oldScreenWidth < 768

    // 移动端切换时自动隐藏侧边栏
    if (isMobile.value) {
      isMobileSidebarVisible.value = false
    }

    // 从移动端切换到桌面端时，恢复展开状态
    if (wasMobile && isDesktop.value) {
      isCollapsed.value = false
    }

    // 从桌面端切换到移动端时，重置折叠状态
    if (wasDesktop && isMobile.value) {
      // 移动端不需要折叠状态，保持展开以便显示完整菜单
    }
  }

  // 切换侧边栏状态
  const toggleSidebar = () => {
    if (isMobile.value) {
      // 移动端切换显示/隐藏
      isMobileSidebarVisible.value = !isMobileSidebarVisible.value
    } else {
      // 桌面端切换折叠状态（平板端不允许切换，始终折叠）
      if (isDesktop.value) {
        isCollapsed.value = !isCollapsed.value
      }
    }
  }

  // 设置侧边栏折叠状态
  const setSidebarCollapsed = (collapsed) => {
    if (isDesktop.value) {
      isCollapsed.value = collapsed
    }
  }

  // 隐藏移动端侧边栏
  const hideMobileSidebar = () => {
    if (isMobile.value) {
      isMobileSidebarVisible.value = false
    }
  }

  // 初始化
  const init = () => {
    handleResize()
    window.addEventListener('resize', handleResize)
  }

  // 清理
  const destroy = () => {
    window.removeEventListener('resize', handleResize)
  }

  return {
    // 状态
    screenWidth,
    isCollapsed,
    isMobileSidebarVisible,
    actualCollapsed,
    sidebarWidth,

    // 设备检测
    isMobile,
    isTablet,
    isDesktop,

    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    hideMobileSidebar,
    init,
    destroy
  }
})
