import request from '@/utils/request'

/**
 * 获取图片验证码
 * @returns {Promise} 验证码数据
 */
export const getCaptcha = () => {
  return request({
    url: '/auth/captcha',
    method: 'get'
  })
}

/**
 * 用户登录
 * @param {Object} data 登录数据
 * @param {string} data.username 用户名
 * @param {string} data.password 密码
 * @param {string} data.captchaId 验证码ID
 * @param {string} data.captcha 验证码内容
 * @param {string} [data.googleAuthCode] 谷歌验证码（可选，当用户启用谷歌验证器时需要）
 * @returns {Promise} 登录结果
 * @description 如果用户启用了谷歌验证器，首次登录会返回错误码9999，需要提供googleAuthCode参数重新登录
 */
export const login = (data) => {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户退出登录
 * @returns {Promise} 退出登录结果
 */
export const logout = () => {
  return request({
    url: '/auth/logout',
    method: 'get'
  })
}
