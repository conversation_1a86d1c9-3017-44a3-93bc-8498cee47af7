import request from '@/utils/request'

/**
 * 登录日志管理 API
 */

/**
 * 分页查询登录日志
 * @param {Object} params 查询参数
 * @param {number} params.current 页码，从1开始
 * @param {number} params.size 每页大小
 * @param {string} params.userId 用户ID
 * @param {string} params.username 用户名
 * @param {string} params.clientIp 客户端IP
 * @param {number} params.loginStatus 登录状态(0-失败,1-成功)
 * @param {number} params.isSuspicious 是否可疑(0-否,1-是)
 * @param {string} params.riskLevel 风险等级(LOW/MEDIUM/HIGH)
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 分页数据
 */
export function getLoginLogPage(params) {
  return request({
    url: '/system/login-log/page',
    method: 'get',
    params
  })
}

/**
 * 获取登录日志详情
 * @param {string} id 登录日志ID
 * @returns {Promise} 登录日志详情
 */
export function getLoginLogById(id) {
  return request({
    url: `/system/login-log/${id}`,
    method: 'get'
  })
}

/**
 * 获取用户最近登录记录
 * @param {string} userId 用户ID
 * @param {Object} params 查询参数
 * @param {number} params.limit 限制数量，默认10
 * @returns {Promise} 最近登录记录列表
 */
export function getUserRecentLogins(userId, params = {}) {
  return request({
    url: `/system/login-log/recent/${userId}`,
    method: 'get',
    params
  })
}

/**
 * 获取登录统计信息
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 统计信息
 */
export function getLoginStatistics(params = {}) {
  return request({
    url: '/system/login-log/statistics',
    method: 'get',
    params
  })
}



/**
 * 统计用户登录次数
 * @param {string} userId 用户ID
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {number} params.loginStatus 登录状态
 * @returns {Promise} 统计结果
 */
export function getUserLoginCount(userId, params = {}) {
  return request({
    url: `/system/login-log/count/user/${userId}`,
    method: 'get',
    params
  })
}

/**
 * 统计IP地址登录次数
 * @param {string} clientIp 客户端IP
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {number} params.loginStatus 登录状态
 * @returns {Promise} 统计结果
 */
export function getIpLoginCount(clientIp, params = {}) {
  return request({
    url: `/system/login-log/count/ip/${clientIp}`,
    method: 'get',
    params
  })
}

/**
 * 清理过期登录日志
 * @param {Object} params 清理参数
 * @param {number} params.days 清理多少天前的记录，默认30天
 * @returns {Promise} 清理结果
 */
export function cleanupExpiredLogs(params = {}) {
  return request({
    url: '/system/login-log/cleanup',
    method: 'delete',
    params
  })
}

/**
 * 清空所有登录日志
 * @returns {Promise} 清空结果
 */
export function clearAllLogs() {
  return request({
    url: '/system/login-log/clear',
    method: 'delete'
  })
}

/**
 * 导出登录日志
 * @param {Object} params 导出参数
 * @param {string} params.userId 用户ID
 * @param {string} params.username 用户名
 * @param {string} params.clientIp 客户端IP
 * @param {number} params.loginStatus 登录状态
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 导出结果
 */
export function exportLoginLogs(params = {}) {
  return request({
    url: '/system/login-log/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取登录趋势数据
 * @param {Object} params 查询参数
 * @param {number} params.days 统计天数，默认7天
 * @returns {Promise} 趋势数据
 */
export function getLoginTrend(params = {}) {
  return request({
    url: '/system/login-log/trend',
    method: 'get',
    params
  })
}

/**
 * 获取热门登录IP地址
 * @param {Object} params 查询参数
 * @param {number} params.limit 限制数量，默认10
 * @param {number} params.days 统计天数，默认7天
 * @returns {Promise} 热门IP列表
 */
export function getTopLoginIps(params = {}) {
  return request({
    url: '/system/login-log/top-ips',
    method: 'get',
    params
  })
}

/**
 * 获取登录设备类型统计
 * @param {Object} params 查询参数
 * @param {number} params.days 统计天数，默认7天
 * @returns {Promise} 设备类型统计
 */
export function getDeviceStats(params = {}) {
  return request({
    url: '/system/login-log/device-stats',
    method: 'get',
    params
  })
}

/**
 * 获取地理位置统计
 * @param {Object} params 查询参数
 * @param {number} params.days 统计天数，默认7天
 * @returns {Promise} 地理位置统计
 */
export function getLocationStats(params = {}) {
  return request({
    url: '/system/login-log/location-stats',
    method: 'get',
    params
  })
}
