import { useMenuStore } from '@/stores/menu'

/**
 * 菜单工具类
 */
class MenuHelper {
  constructor() {
    this.menuStore = null
  }

  /**
   * 获取菜单store实例
   * @returns {Object}
   */
  getMenuStore() {
    if (!this.menuStore) {
      this.menuStore = useMenuStore()
    }
    return this.menuStore
  }

  /**
   * 根据路径查找菜单项
   * @param {string} path 路由路径
   * @returns {Object|null}
   */
  findMenuByPath(path) {
    const store = this.getMenuStore()
    return store.findMenuByPath(path)
  }

  /**
   * 根据菜单编码查找菜单项
   * @param {string} menuCode 菜单编码
   * @returns {Object|null}
   */
  findMenuByCode(menuCode) {
    const store = this.getMenuStore()
    return store.findMenuByCode(menuCode)
  }

  /**
   * 获取扁平化的菜单列表
   * @returns {Array<Object>}
   */
  getFlatMenus() {
    const store = this.getMenuStore()
    return store.getFlatMenus()
  }

  /**
   * 获取可见的菜单列表
   * @returns {Array<Object>}
   */
  getVisibleMenus() {
    const store = this.getMenuStore()
    return store.getVisibleMenus()
  }

  /**
   * 获取所有菜单
   * @returns {Array<Object>}
   */
  getAllMenus() {
    const store = this.getMenuStore()
    return store.menus
  }

  /**
   * 检查菜单是否已加载
   * @returns {boolean}
   */
  isMenuLoaded() {
    const store = this.getMenuStore()
    return store.isLoaded
  }

  /**
   * 生成面包屑导航
   * @param {string} currentPath 当前路径
   * @returns {Array<Object>} 面包屑数组
   */
  generateBreadcrumb(currentPath) {
    const breadcrumb = []
    const flatMenus = this.getFlatMenus()

    // 找到当前菜单项（现在路径已经在 store 中统一处理过了）
    const currentMenu = flatMenus.find(menu => menu.routePath === currentPath)

    if (!currentMenu) {
      return breadcrumb
    }

    // 递归查找父级菜单
    const findParents = (menuId, menus = this.getAllMenus()) => {
      for (const menu of menus) {
        if (menu.id === menuId) {
          return [menu]
        }
        if (menu.children && menu.children.length > 0) {
          const found = findParents(menuId, menu.children)
          if (found.length > 0) {
            return [menu, ...found]
          }
        }
      }
      return []
    }

    // 构建面包屑路径
    const parents = findParents(currentMenu.id)
    return parents.map(menu => ({
      id: menu.id,
      name: menu.menuName,
      path: menu.routePath,
      icon: menu.icon
    }))
  }

  /**
   * 检查菜单项是否可见
   * @param {Object} menu 菜单项
   * @returns {boolean}
   */
  isMenuVisible(menu) {
    return menu && menu.visible === 1
  }

  /**
   * 检查是否为外部链接
   * @param {Object} menu 菜单项
   * @returns {boolean}
   */
  isExternalLink(menu) {
    return menu && menu.externalLink === 1
  }

  /**
   * 获取菜单的打开方式
   * @param {Object} menu 菜单项
   * @returns {string} '_self' | '_blank'
   */
  getOpenMode(menu) {
    return menu?.openMode || '_self'
  }

  /**
   * 解析路由参数
   * @param {Object} menu 菜单项
   * @returns {Object|null} 解析后的参数对象
   */
  parseRouteParams(menu) {
    if (!menu?.routeParams) {
      return null
    }
    
    try {
      return JSON.parse(menu.routeParams)
    } catch (error) {
      console.error('解析菜单路由参数失败:', error)
      return null
    }
  }

  /**
   * 根据菜单类型过滤菜单
   * @param {number} menuType 菜单类型：1(目录)、2(菜单)、3(按钮)
   * @returns {Array<Object>} 过滤后的菜单列表
   */
  filterMenusByType(menuType) {
    const flatMenus = this.getFlatMenus()
    return flatMenus.filter(menu => menu.menuType === menuType)
  }

  /**
   * 获取导航菜单（目录和菜单，排除按钮）
   * @returns {Array<Object>} 导航菜单列表
   */
  getNavigationMenus() {
    const filterNavMenus = (menus) => {
      return menus.filter(menu => {
        // 只保留目录(1)和菜单(2)，排除按钮(3)
        if (menu.menuType === 3) {
          return false
        }
        
        // 检查可见性
        if (!this.isMenuVisible(menu)) {
          return false
        }
        
        // 递归处理子菜单
        if (menu.children && menu.children.length > 0) {
          menu.children = filterNavMenus(menu.children)
        }
        
        return true
      })
    }
    
    return filterNavMenus(this.getAllMenus())
  }
}

// 创建单例实例
const menuHelper = new MenuHelper()

// 导出便捷方法
export const findMenuByPath = (path) => menuHelper.findMenuByPath(path)
export const findMenuByCode = (menuCode) => menuHelper.findMenuByCode(menuCode)
export const getFlatMenus = () => menuHelper.getFlatMenus()
export const getVisibleMenus = () => menuHelper.getVisibleMenus()
export const getAllMenus = () => menuHelper.getAllMenus()
export const isMenuLoaded = () => menuHelper.isMenuLoaded()
export const generateBreadcrumb = (currentPath) => menuHelper.generateBreadcrumb(currentPath)
export const isMenuVisible = (menu) => menuHelper.isMenuVisible(menu)
export const isExternalLink = (menu) => menuHelper.isExternalLink(menu)
export const getOpenMode = (menu) => menuHelper.getOpenMode(menu)
export const parseRouteParams = (menu) => menuHelper.parseRouteParams(menu)
export const filterMenusByType = (menuType) => menuHelper.filterMenusByType(menuType)
export const getNavigationMenus = () => menuHelper.getNavigationMenus()

// 导出类和实例
export { MenuHelper }
export default menuHelper
