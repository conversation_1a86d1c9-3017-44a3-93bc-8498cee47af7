import request from '@/utils/request'

/**
 * 用户管理 API
 */

/**
 * 分页查询用户列表
 * @param {Object} params 查询参数
 * @param {number} params.current 页码，从1开始
 * @param {number} params.size 每页大小
 * @param {string} params.username 用户名，支持模糊查询
 * @param {string} params.userTypeId 用户类型ID
 * @param {number} params.accountLocked 账户锁定状态，0=未锁定，1=已锁定
 * @returns {Promise} 分页数据
 */
export function getUserPage(params) {
  return request({
    url: '/user/page',
    method: 'get',
    params
  })
}

/**
 * 获取全部用户类型列表
 * @returns {Promise} 用户类型列表
 */
export function getUserTypes() {
  return request({
    url: '/user/types',
    method: 'get'
  })
}

/**
 * 获取当前用户信息
 * @returns {Promise} 当前登录用户信息
 * @description 获取当前登录用户的基本信息，包含用户类型名称等扩展信息
 */
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

/**
 * 修改当前用户密码
 * @param {Object} data 密码修改数据
 * @param {string} data.oldPassword 当前密码，用于身份验证
 * @param {string} data.newPassword 新密码，长度6-100字符
 * @param {string} data.confirmPassword 确认新密码，必须与新密码一致
 * @returns {Promise} 修改结果
 */
export function changePassword(data) {
  return request({
    url: '/user/change-password',
    method: 'post',
    data
  })
}

/**
 * 新增用户
 * @param {Object} data 用户数据
 * @param {string} data.username 用户名，长度1-50字符，必须唯一
 * @param {string} data.password 用户密码，长度6-100字符
 * @param {string} data.userTypeId 用户类型ID
 * @returns {Promise} 响应结果
 */
export function createUser(data) {
  return request({
    url: '/user',
    method: 'post',
    data
  })
}

/**
 * 修改用户信息
 * @param {string} id 用户ID
 * @param {Object} data 用户数据
 * @param {string} data.username 用户名
 * @param {string} data.userTypeId 用户类型ID
 * @param {number} data.accountLocked 账户锁定状态，0=未锁定，1=已锁定
 * @returns {Promise} 响应结果
 */
export function updateUser(id, data) {
  return request({
    url: `/user/${id}`,
    method: 'put',
    data
  })
}

/**
 * 重置用户密码
 * @param {string} id 用户ID
 * @param {string} newPassword 新密码，长度6-100字符
 * @returns {Promise} 响应结果
 */
export function resetUserPassword(id, newPassword) {
  return request({
    url: `/user/${id}/reset-password`,
    method: 'post',
    data: {
      newPassword
    }
  })
}

/**
 * 重置用户谷歌验证KEY
 * @param {string} id 用户ID
 * @returns {Promise} 响应结果
 */
export function resetUserGoogleAuth(id) {
  return request({
    url: `/user/${id}/reset-google-auth`,
    method: 'post'
  })
}

/**
 * 删除用户
 * @param {string} id 用户ID
 * @returns {Promise} 响应结果
 */
export function deleteUser(id) {
  return request({
    url: `/user/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除用户
 * @param {Array<string>} userIds 用户ID列表
 * @returns {Promise} 响应结果
 */
export function batchDeleteUsers(userIds) {
  return request({
    url: '/user/batch',
    method: 'delete',
    data: userIds
  })
}

/**
 * 锁定用户
 * @param {string} id 用户ID
 * @returns {Promise} 响应结果
 */
export function lockUser(id) {
  return request({
    url: `/user/${id}/lock`,
    method: 'post'
  })
}

/**
 * 解锁用户
 * @param {string} id 用户ID
 * @returns {Promise} 响应结果
 */
export function unlockUser(id) {
  return request({
    url: `/user/${id}/unlock`,
    method: 'post'
  })
}

/**
 * 批量锁定用户
 * @param {Array<string>} userIds 用户ID列表
 * @returns {Promise} 响应结果
 */
export function batchLockUsers(userIds) {
  return request({
    url: '/user/batch/lock',
    method: 'post',
    data: userIds
  })
}

/**
 * 批量解锁用户
 * @param {Array<string>} userIds 用户ID列表
 * @returns {Promise} 响应结果
 */
export function batchUnlockUsers(userIds) {
  return request({
    url: '/user/batch/unlock',
    method: 'post',
    data: userIds
  })
}
