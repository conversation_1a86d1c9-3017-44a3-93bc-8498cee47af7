<template>
  <div class="google-auth-management">
    <!-- 当前状态显示 -->
    <div class="auth-status">
      <div class="status-item">
        <el-icon class="status-icon" :class="statusIconClass">
          <component :is="statusIcon" />
        </el-icon>
        <div class="status-content">
          <div class="status-label">谷歌验证器状态</div>
          <div class="status-value" :class="statusTextClass">
            {{ statusText }}
          </div>
        </div>
      </div>
      <div v-if="userInfo?.hasGoogleAuth" class="status-actions">
        <el-button 
          type="danger" 
          size="small" 
          :loading="resetting"
          @click="handleReset"
        >
          解绑验证器
        </el-button>
      </div>
    </div>

    <!-- 未设置状态 - 显示设置向导 -->
    <div v-if="!userInfo?.hasGoogleAuth" class="setup-section">
      <div v-if="!setupData" class="setup-start">
        <div class="setup-description">
          <h4>什么是谷歌验证器？</h4>
          <p>谷歌验证器是一种基于时间的一次性密码（TOTP）应用，为您的账户提供额外的安全保护。</p>
          
          <h4>设置步骤：</h4>
          <ol>
            <li>在手机上下载并安装谷歌验证器应用</li>
            <li>点击下方"开始设置"按钮获取设置信息</li>
            <li>使用应用扫描二维码或手动输入密钥</li>
            <li>输入验证器生成的6位数字完成设置</li>
          </ol>
        </div>
        
        <div class="setup-actions">
          <el-button 
            type="primary" 
            :loading="loading"
            @click="startSetup"
          >
            开始设置谷歌验证器
          </el-button>
        </div>
      </div>

      <!-- 设置过程 -->
      <div v-else class="setup-process">
        <el-steps :active="currentStep" align-center>
          <el-step title="扫描二维码" />
          <el-step title="输入验证码" />
          <el-step title="设置完成" />
        </el-steps>

        <!-- 步骤1: 扫描二维码 -->
        <div v-if="currentStep === 0" class="setup-step">
          <div class="qr-section">
            <div class="qr-code">
              <canvas ref="qrCanvas" width="200" height="200"></canvas>
              <div v-if="qrGenerating" class="qr-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>生成二维码中...</span>
              </div>
            </div>
            <div class="qr-info">
              <h4>扫描二维码</h4>
              <p>使用谷歌验证器应用扫描左侧二维码，或手动输入以下密钥：</p>
              <div class="secret-key">
                <el-input
                  :value="setupData.secretKey"
                  readonly
                  class="secret-input"
                >
                  <template #append>
                    <el-button @click="copySecret">复制</el-button>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
          
          <div class="step-actions">
            <el-button @click="cancelSetup">取消</el-button>
            <el-button type="primary" @click="nextStep">下一步</el-button>
          </div>
        </div>

        <!-- 步骤2: 输入验证码 -->
        <div v-if="currentStep === 1" class="setup-step">
          <div class="verify-section">
            <h4>输入验证码</h4>
            <p>请输入谷歌验证器应用中显示的6位数字验证码：</p>
            
            <div class="verify-code-container">
              <VerificationCodeInput
                ref="verifyCodeInputRef"
                v-model="verifyForm.code"
                :auto-submit="true"
                @complete="handleVerifyCodeComplete"
                @submit="confirmSetup"
              />
            </div>
          </div>
          
          <div class="step-actions">
            <el-button @click="prevStep">上一步</el-button>
            <el-button 
              type="primary" 
              :loading="confirming"
              @click="confirmSetup"
            >
              确认设置
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 已设置状态 - 显示验证功能 -->
    <div v-else class="verify-section">
      <div class="verify-description">
        <h4>验证谷歌验证器</h4>
        <p>您可以输入验证码来测试谷歌验证器是否正常工作：</p>
      </div>
      
      <div class="test-code-container">
        <VerificationCodeInput
          ref="testCodeInputRef"
          v-model="testForm.code"
          :auto-submit="true"
          @complete="handleTestCodeComplete"
          @submit="testVerify"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import {
  getGoogleAuthSetup,
  confirmGoogleAuthSetup,
  verifyGoogleAuth,
  resetGoogleAuth
} from '@/api/googleAuth'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Close,
  Loading
} from '@element-plus/icons-vue'
import QRCode from 'qrcode'
import VerificationCodeInput from '@/components/VerificationCodeInput.vue'

// 使用auth store
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const confirming = ref(false)
const testing = ref(false)
const resetting = ref(false)
const setupData = ref(null)
const currentStep = ref(0)
const qrGenerating = ref(false)

// 表单引用
const qrCanvas = ref()
const verifyCodeInputRef = ref()
const testCodeInputRef = ref()

// 计算属性
const userInfo = computed(() => authStore.userInfo)

const statusIcon = computed(() => {
  return userInfo.value?.hasGoogleAuth ? Check : Close
})

const statusIconClass = computed(() => {
  return userInfo.value?.hasGoogleAuth ? 'status-success' : 'status-danger'
})

const statusText = computed(() => {
  return userInfo.value?.hasGoogleAuth ? '已设置' : '未设置'
})

const statusTextClass = computed(() => {
  return userInfo.value?.hasGoogleAuth ? 'text-success' : 'text-danger'
})

// 表单数据
const verifyForm = reactive({
  code: ''
})

const testForm = reactive({
  code: ''
})

// 验证码输入完成处理
const handleVerifyCodeComplete = (code) => {
  console.log('验证码输入完成:', code)
}

const handleTestCodeComplete = (code) => {
  console.log('测试验证码输入完成:', code)
}

// 生成二维码
const generateQRCode = async (otpAuthUrl) => {
  try {
    qrGenerating.value = true
    await nextTick() // 确保canvas元素已渲染

    if (qrCanvas.value) {
      await QRCode.toCanvas(qrCanvas.value, otpAuthUrl, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
    }
  } catch (error) {
    // 二维码生成失败是前端问题，保留错误提示
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  } finally {
    qrGenerating.value = false
  }
}

// 开始设置
const startSetup = async () => {
  try {
    loading.value = true
    const response = await getGoogleAuthSetup()
    setupData.value = response
    currentStep.value = 0

    // 生成二维码
    if (response.otpAuthUrl) {
      await generateQRCode(response.otpAuthUrl)
    }
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('获取设置信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 取消设置
const cancelSetup = () => {
  setupData.value = null
  currentStep.value = 0
  verifyForm.code = ''
}

// 下一步
const nextStep = () => {
  if (currentStep.value < 1) {
    currentStep.value++
  }
}

// 上一步
const prevStep = async () => {
  if (currentStep.value > 0) {
    currentStep.value--

    // 如果返回到第一步，重新生成二维码
    if (currentStep.value === 0 && setupData.value?.otpAuthUrl) {
      await generateQRCode(setupData.value.otpAuthUrl)
    }
  }
}

// 复制密钥
const copySecret = async () => {
  try {
    await navigator.clipboard.writeText(setupData.value.secretKey)
    ElMessage.success('密钥已复制到剪贴板')
  } catch (error) {
    // 复制失败是前端操作问题，保留错误提示
    ElMessage.error('复制失败，请手动复制')
  }
}

// 确认设置
const confirmSetup = async () => {
  // 检查验证码是否完整
  if (!verifyForm.code || verifyForm.code.length !== 6) {
    ElMessage.warning('请输入完整的6位验证码')
    return
  }

  try {
    confirming.value = true

    await confirmGoogleAuthSetup({
      code: verifyForm.code
    })

    ElMessage.success('谷歌验证器设置成功')

    // 重置状态
    setupData.value = null
    currentStep.value = 0
    verifyForm.code = ''

    // 刷新用户信息
    await authStore.fetchUserInfo(true) // 强制刷新
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('设置失败:', error)
    // 清空验证码输入框
    if (verifyCodeInputRef.value) {
      verifyCodeInputRef.value.clear()
    }
  } finally {
    confirming.value = false
  }
}

// 测试验证
const testVerify = async () => {
  // 检查验证码是否完整
  if (!testForm.code || testForm.code.length !== 6) {
    ElMessage.warning('请输入完整的6位验证码')
    return
  }

  try {
    testing.value = true

    await verifyGoogleAuth({
      code: testForm.code
    })

    ElMessage.success('验证成功')
    testForm.code = ''

    // 清空验证码输入框
    if (testCodeInputRef.value) {
      testCodeInputRef.value.clear()
    }
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('验证失败:', error)
    // 清空验证码输入框
    if (testCodeInputRef.value) {
      testCodeInputRef.value.clear()
    }
  } finally {
    testing.value = false
  }
}

// 监听步骤变化，确保二维码正确显示
watch(currentStep, async (newStep) => {
  if (newStep === 0 && setupData.value?.otpAuthUrl) {
    // 延迟一下确保DOM已更新
    await nextTick()
    await generateQRCode(setupData.value.otpAuthUrl)
  }
})

// 重置验证器
const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '解绑后将无法使用谷歌验证器进行身份验证，确定要解绑吗？',
      '确认解绑',
      {
        confirmButtonText: '确定解绑',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    resetting.value = true

    await resetGoogleAuth()

    ElMessage.success('谷歌验证器解绑成功')
    
    // 刷新用户信息
    await authStore.fetchUserInfo(true) // 强制刷新
  } catch (error) {
    if (error !== 'cancel') {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('解绑失败:', error)
    }
  } finally {
    resetting.value = false
  }
}
</script>

<style scoped>
.google-auth-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 状态显示 */
.auth-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  font-size: 20px;
}

.status-icon.status-success {
  color: var(--el-color-success);
}

.status-icon.status-danger {
  color: var(--el-color-danger);
}

.status-content {
  display: flex;
  flex-direction: column;
}

.status-label {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.status-value {
  font-size: 14px;
}

.status-value.text-success {
  color: var(--el-color-success);
}

.status-value.text-danger {
  color: var(--el-color-danger);
}

/* 设置区域 */
.setup-section {
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
  padding: 20px;
}

.setup-description h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.setup-description p {
  margin: 0 0 16px 0;
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.5;
}

.setup-description ol {
  margin: 0 0 16px 0;
  padding-left: 20px;
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.6;
}

.setup-actions {
  text-align: center;
  margin-top: 20px;
}

/* 设置过程 */
.setup-process {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.setup-step {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 二维码区域 */
.qr-section {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.qr-code {
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-code canvas {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
}

.qr-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 6px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.qr-info {
  flex: 1;
}

.qr-info h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.qr-info p {
  margin: 0 0 16px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
  line-height: 1.5;
}

.secret-key {
  margin-top: 16px;
}

.secret-input {
  font-family: monospace;
}


/* 验证区域 */
.verify-section {
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
  padding: 20px;
}

.verify-section h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  text-align: center;
}

.verify-section p {
  margin: 0 0 24px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
  text-align: center;
}

.verify-code-container,
.test-code-container {
  margin: 24px 0;
  display: flex;
  justify-content: center;
}

.verify-description h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.verify-description p {
  margin: 0 0 16px 0;
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.5;
}

.verify-input {
  max-width: 200px;
}

/* 步骤操作 */
.step-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .auth-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .qr-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .qr-code canvas {
    width: 160px !important;
    height: 160px !important;
  }
  
  .step-actions {
    flex-direction: column-reverse;
  }
  
  .step-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .setup-section,
  .verify-section {
    padding: 16px;
  }
  
  .qr-code canvas {
    width: 140px !important;
    height: 140px !important;
  }
}
</style>
