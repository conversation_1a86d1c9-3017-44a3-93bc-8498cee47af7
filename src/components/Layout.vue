<template>
  <div class="admin-layout">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside
        :width="layoutStore.sidebarWidth"
        class="layout-sidebar"
        :class="{
          'mobile-visible': layoutStore.isMobileSidebarVisible,
          'mobile-hidden': !layoutStore.isMobileSidebarVisible
        }"
      >
        <Sidebar />
      </el-aside>

      <!-- 移动端遮罩层 - 移到侧边栏外面 -->
      <div
        v-if="layoutStore.isMobile && layoutStore.isMobileSidebarVisible"
        class="sidebar-overlay"
        @click="layoutStore.hideMobileSidebar"
        style="background: rgba(0, 0, 0, 0.5);"
      ></div>
      
      <!-- 主内容区域 -->
      <el-container class="layout-main">
        <!-- 顶部导航 -->
        <el-header class="layout-header">
          <Header />
        </el-header>

        <!-- 标签页导航 -->
        <TagsView />

        <!-- 内容区域 -->
        <el-main class="layout-content">
          <div class="content-wrapper">
            <router-view v-slot="{ Component }">
              <keep-alive :include="cachedViews">
                <transition name="fade" mode="out-in">
                  <component :is="Component" />
                </transition>
              </keep-alive>
            </router-view>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { onMounted, onUnmounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'
import TagsView from './TagsView.vue'
import { useLayoutStore } from '@/stores/layout'
import { useTagsViewStore } from '@/stores/tagsView'
import { routeRefreshEvent, refreshUserRoutes } from '@/utils/routeRefresh'

export default {
  name: 'Layout',
  components: {
    Header,
    Sidebar,
    TagsView
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const layoutStore = useLayoutStore()
    const tagsViewStore = useTagsViewStore()

    // 计算属性 - 缓存的视图列表
    const cachedViews = computed(() => tagsViewStore.cachedViews)

    // 路由刷新处理函数
    const handleRouteRefresh = async () => {
      try {
        await refreshUserRoutes(router)
        // 路由刷新后，清理无效的标签页
        const cleanResult = tagsViewStore.cleanInvalidViews(router, route.path)

        // 如果当前页面对应的标签被清理了，需要重定向
        if (cleanResult.needRedirect && cleanResult.redirectPath) {
          console.log(`当前页面无效，重定向到: ${cleanResult.redirectPath}`)
          router.push(cleanResult.redirectPath)
        }
      } catch (error) {
        console.error('路由刷新失败:', error)
      }
    }

    // 添加标签页视图
    const addTagsView = () => {
      // 排除不需要添加到标签页的路由
      const excludeRoutes = ['/login', '/session-expired', '/403', '/404', '/system-error', '/redirect']
      const shouldExclude = excludeRoutes.some(path => route.path.startsWith(path))

      if (!shouldExclude && route.name) {
        // 特殊处理 iframe 路由的标题
        let title = route.meta?.title || route.name
        if (route.name === 'IframeView' && route.query.title) {
          title = route.query.title
        }

        tagsViewStore.addView({
          path: route.path,
          name: route.name,
          title: title,
          meta: route.meta || {},
          query: route.query || {},
          params: route.params || {}
        })
      }
    }

    // 监听路由变化，自动添加标签页
    watch(route, () => {
      addTagsView()
    }, { immediate: true })

    // 设置动态视口高度（解决移动端浏览器工具栏遮挡问题）
    const setDynamicViewportHeight = () => {
      // 获取实际的视口高度（排除浏览器工具栏）
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)

      // 检测横屏状态
      const isLandscape = window.innerWidth > window.innerHeight
      document.documentElement.style.setProperty('--is-landscape', isLandscape ? '1' : '0')

      // 横屏时的特殊处理
      if (isLandscape && window.innerWidth <= 1024) { // 移动设备横屏
        // 横屏时减少一些高度，为浏览器工具栏留出更多空间
        const adjustedVh = (window.innerHeight - 10) * 0.01
        document.documentElement.style.setProperty('--vh', `${adjustedVh}px`)
      }
    }

    // 组件挂载时添加路由刷新监听器和初始化布局
    onMounted(() => {
      routeRefreshEvent.addListener(handleRouteRefresh)
      layoutStore.init() // 初始化响应式布局
      tagsViewStore.initTagsView() // 初始化标签页

      // 设置动态视口高度
      setDynamicViewportHeight()
      window.addEventListener('resize', setDynamicViewportHeight)
      window.addEventListener('orientationchange', () => {
        // 横屏切换时延迟执行，等待浏览器完成布局调整
        setTimeout(setDynamicViewportHeight, 100)
      })
    })

    // 组件卸载时移除路由刷新监听器和清理布局
    onUnmounted(() => {
      routeRefreshEvent.removeListener(handleRouteRefresh)
      layoutStore.destroy() // 清理响应式布局

      // 清理动态视口高度监听器
      window.removeEventListener('resize', setDynamicViewportHeight)
      window.removeEventListener('orientationchange', setDynamicViewportHeight)
    })

    return {
      layoutStore,
      cachedViews
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-layout {
  height: 100vh;
  overflow: hidden;
}

.layout-container {
  height: 100%;
}

.layout-sidebar {
  background: var(--bg-color-sidebar);
  transition: width 0.3s ease;
  overflow: hidden;
  box-shadow: var(--box-shadow-base);
  z-index: 1001;
}

.layout-main {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.layout-header {
  background: var(--bg-color-header);
  box-shadow: var(--box-shadow-base);
  padding: 0;
  height: var(--header-height);
  z-index: 1000;
}

.layout-content {
  background: var(--bg-color-page);
  padding: 0;
  overflow-y: auto;
  flex: 1;
}

/* .content-wrapper 样式已移至全局样式文件 */

// 页面切换动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 侧边栏遮罩层
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 280px; // 从侧边栏右侧开始，不覆盖侧边栏
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001; // 确保在主内容区域上方，可以被点击
  cursor: pointer;
}

// 响应式设计
// 移动端 (< 768px)
@media (max-width: 767px) {
  .admin-layout {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* JavaScript计算的动态高度 */
    height: 100dvh; /* 现代浏览器的动态视口高度 */
    overflow: hidden;
    position: relative;
  }

  .layout-container {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* JavaScript计算的动态高度 */
    height: 100dvh; /* 现代浏览器的动态视口高度 */
    overflow: hidden;
  }

  .layout-sidebar {
    position: fixed;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* JavaScript计算的动态高度 */
    height: 100dvh; /* 现代浏览器的动态视口高度 */
    top: 0;
    left: 0;
    z-index: 1002; // 侧边栏层级
    width: 280px !important; // 移动端固定宽度
    transform: translateX(-100%); // 默认隐藏
    transition: transform 0.3s ease;

    // 移动端显示状态
    &.mobile-visible {
      transform: translateX(0); // 显示侧边栏
    }

    // 移动端隐藏状态（默认状态，可以省略）
    &.mobile-hidden {
      transform: translateX(-100%); // 隐藏侧边栏
    }
  }

  // 移动端遮罩层样式调整
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0 !important; // 移动端遮罩层覆盖整个屏幕
    right: 0;
    bottom: 0;
    z-index: 1001 !important; // 确保在主内容上方，但在侧边栏下方
  }

  .layout-main {
    margin-left: 0 !important;
    width: 100%;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* JavaScript计算的动态高度 */
    height: 100dvh; /* 现代浏览器的动态视口高度 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .layout-header {
    flex-shrink: 0;
    height: var(--header-height);
  }

  .layout-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch; // iOS平滑滚动
  }

  .content-wrapper {
    padding: 12px;
    min-height: 100%;
  }
}

// 移动端横屏适配
@media (max-width: 1024px) and (orientation: landscape) {
  .admin-layout {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    height: 100dvh;
  }

  .layout-container {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    height: 100dvh;
  }

  .layout-sidebar {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    height: 100dvh;
  }

  .layout-main {
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100);
    height: 100dvh;
  }

  .layout-header {
    flex-shrink: 0;
    height: var(--header-height);
    min-height: 50px; // 横屏时减少头部高度
  }

  .layout-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    // 横屏时确保内容区域有足够的高度
    min-height: calc(100vh - var(--header-height) - 20px);
    min-height: calc(calc(var(--vh, 1vh) * 100) - var(--header-height) - 20px);
    min-height: calc(100dvh - var(--header-height) - 20px);
  }

  .content-wrapper {
    padding: 8px 12px; // 横屏时减少垂直内边距
    min-height: 100%;
  }
}

// 平板端 (768px - 1023px)
@media (min-width: 768px) and (max-width: 1023px) {
  .layout-sidebar {
    // 平板端默认折叠
    width: 64px !important;
  }

  .content-wrapper {
    padding: 16px;
  }
}

// 桌面端 (>= 1024px)
@media (min-width: 1024px) {
  .content-wrapper {
    padding: 24px;
  }
}
</style>
