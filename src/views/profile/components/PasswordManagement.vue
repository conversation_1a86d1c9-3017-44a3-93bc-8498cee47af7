<template>
  <div class="password-management">
    <div class="password-info">
      <div class="info-item">
        <el-icon class="info-icon"><Clock /></el-icon>
        <div class="info-content">
          <div class="info-label">密码最后修改时间</div>
          <div class="info-value">{{ formatDateTime(userInfo?.passwordUpdateTime) }}</div>
        </div>
      </div>
      <div class="info-item">
        <el-icon class="info-icon"><Lock /></el-icon>
        <div class="info-content">
          <div class="info-label">密码安全建议</div>
          <div class="info-value">定期更换密码，使用强密码保护账户安全</div>
        </div>
      </div>
    </div>

    <div class="password-form-section">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="top"
        @submit.prevent="handleSubmit"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input
            v-model="form.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="form.newPassword"
            type="password"
            placeholder="请输入新密码（6-100字符）"
            show-password
            clearable
            @input="checkPasswordStrength"
            @keyup.enter="handleSubmit"
          />
          <!-- 密码强度指示器 -->
          <div v-if="form.newPassword" class="password-strength">
            <div class="strength-label">密码强度：</div>
            <div class="strength-bar">
              <div 
                class="strength-fill" 
                :class="passwordStrength.level"
                :style="{ width: passwordStrength.percentage + '%' }"
              ></div>
            </div>
            <div class="strength-text" :class="passwordStrength.level">
              {{ passwordStrength.text }}
            </div>
          </div>
        </el-form-item>

        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            clearable
            @keyup.enter="handleSubmit"
          />
        </el-form-item>

        <el-form-item>
          <div class="form-actions">
            <el-button @click="resetForm">重置</el-button>
            <el-button 
              type="primary" 
              :loading="loading"
              @click="handleSubmit"
            >
              修改密码
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 密码安全提示 -->
    <div class="security-tips">
      <h4>密码安全建议</h4>
      <ul>
        <li>密码长度至少6个字符，建议8个字符以上</li>
        <li>包含大小写字母、数字和特殊字符</li>
        <li>不要使用常见的密码或个人信息</li>
        <li>定期更换密码，建议3-6个月更换一次</li>
        <li>不要在多个网站使用相同密码</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { changePassword } from '@/api/user'
import { ElMessage } from 'element-plus'
import { Clock, Lock } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'

// 使用auth store
const authStore = useAuthStore()
const formRef = ref()
const loading = ref(false)

// 计算属性
const userInfo = computed(() => authStore.userInfo)

// 表单数据
const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码强度
const passwordStrength = ref({
  level: 'weak',
  percentage: 0,
  text: '弱'
})

// 表单验证规则
const rules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 100, message: '密码长度在 6 到 100 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 确认密码验证
function validateConfirmPassword(rule, value, callback) {
  if (value === '') {
    callback(new Error('请再次输入新密码'))
  } else if (value !== form.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 检查密码强度
const checkPasswordStrength = () => {
  const password = form.newPassword
  if (!password) {
    passwordStrength.value = { level: 'weak', percentage: 0, text: '弱' }
    return
  }

  let score = 0
  let checks = 0

  // 长度检查
  if (password.length >= 8) score += 25
  else if (password.length >= 6) score += 15

  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 20
    checks++
  }

  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    score += 20
    checks++
  }

  // 包含数字
  if (/\d/.test(password)) {
    score += 20
    checks++
  }

  // 包含特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) {
    score += 15
    checks++
  }

  // 根据分数确定强度
  if (score >= 80) {
    passwordStrength.value = { level: 'strong', percentage: score, text: '强' }
  } else if (score >= 50) {
    passwordStrength.value = { level: 'medium', percentage: score, text: '中' }
  } else {
    passwordStrength.value = { level: 'weak', percentage: score, text: '弱' }
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordStrength.value = { level: 'weak', percentage: 0, text: '弱' }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    await changePassword({
      oldPassword: form.oldPassword,
      newPassword: form.newPassword,
      confirmPassword: form.confirmPassword
    })

    ElMessage.success('密码修改成功')
    resetForm()
    
    // 刷新用户信息
    await authStore.fetchUserInfo(true) // 强制刷新
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('修改密码失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.password-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 密码信息 */
.password-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-icon {
  color: var(--el-color-primary);
  font-size: 18px;
  margin-top: 2px;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-label {
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.info-value {
  color: var(--el-text-color-regular);
  font-size: 14px;
  line-height: 1.5;
}

/* 表单区域 */
.password-form-section {
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
  padding: 20px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 密码强度指示器 */
.password-strength {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.strength-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: var(--el-border-color-lighter);
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background: var(--el-color-danger);
}

.strength-fill.medium {
  background: var(--el-color-warning);
}

.strength-fill.strong {
  background: var(--el-color-success);
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.strength-text.weak {
  color: var(--el-color-danger);
}

.strength-text.medium {
  color: var(--el-color-warning);
}

.strength-text.strong {
  color: var(--el-color-success);
}

/* 安全提示 */
.security-tips {
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
  padding: 20px;
}

.security-tips h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.6;
}

.security-tips li {
  margin-bottom: 4px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .password-form-section {
    padding: 16px;
  }
  
  .form-actions {
    width: 100%;
    flex-direction: column-reverse;
  }
  .el-button + .el-button{
    margin-left: 0px;
  }

  .form-actions .el-button {
    width: 100%;
  }
  
  .password-strength {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .strength-bar {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .password-info {
    padding: 12px;
  }
  
  .password-form-section {
    padding: 12px;
  }
  
  .security-tips {
    padding: 12px;
  }
  
  .info-item {
    gap: 8px;
  }
  
  .info-icon {
    font-size: 16px;
  }
}
</style>
