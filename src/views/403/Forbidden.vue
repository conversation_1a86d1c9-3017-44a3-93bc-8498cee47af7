<template>
  <div class="forbidden-container">
    <div class="forbidden-content">
      <!-- 图标 -->
      <div class="icon-container">
        <el-icon :size="120" :color="'var(--danger-color)'">
          <Lock />
        </el-icon>
      </div>
      
      <!-- 标题 -->
      <h1 class="title">403</h1>
      <h2 class="subtitle">访问被拒绝</h2>
      
      <!-- 描述 -->
      <p class="description">
        抱歉，您没有权限访问此页面。<br>
        请联系管理员获取相应权限。
      </p>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <el-button type="primary" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上一页
        </el-button>
        <el-button @click="goHome">
          <el-icon><House /></el-icon>
          回到首页
        </el-button>
      </div>
      
      <!-- 权限信息 -->
      <div class="permission-info" v-if="showPermissionInfo">
        <el-collapse>
          <el-collapse-item title="权限详情" name="permission">
            <div class="permission-details">
              <p><strong>当前用户权限：</strong></p>
              <el-tag 
                v-for="permission in userPermissions" 
                :key="permission"
                size="small"
                class="permission-tag"
              >
                {{ permission }}
              </el-tag>
              <p v-if="userPermissions.length === 0" class="no-permission">
                暂无权限
              </p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Lock, ArrowLeft, House } from '@element-plus/icons-vue'
import { getPermissions } from '@/utils/permission'

const router = useRouter()

// 是否显示权限信息（开发环境显示）
const showPermissionInfo = ref(import.meta.env.DEV)

// 获取用户权限
const userPermissions = computed(() => {
  return getPermissions()
})

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 回到首页
const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.forbidden-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-color-page) 0%, var(--border-color-light) 100%);
  padding: 20px;
}

.forbidden-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  background: var(--bg-color-card);
  border-radius: 12px;
  padding: 40px;
  box-shadow: var(--box-shadow-xl);
}

.icon-container {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.title {
  font-size: 72px;
  font-weight: bold;
  color: var(--danger-color);
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 28px;
  color: var(--text-color-secondary);
  margin: 0 0 20px 0;
  font-weight: 500;
}

.description {
  font-size: 16px;
  color: var(--text-color-placeholder);
  line-height: 1.6;
  margin-bottom: 30px;
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.permission-info {
  margin-top: 20px;
  text-align: left;
}

.permission-details {
  padding: 10px 0;
}

.permission-tag {
  margin: 4px;
}

.no-permission {
  color: var(--text-color-placeholder);
  font-style: italic;
  margin: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forbidden-content {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 56px;
  }
  
  .subtitle {
    font-size: 24px;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .actions .el-button {
    width: 200px;
  }
}
</style>
