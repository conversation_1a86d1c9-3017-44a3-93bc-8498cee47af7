/**
 * 自动加载 SVG 图标列表
 * 这个函数会扫描 /src/assets/svg/ 目录下的所有 .svg 文件
 */

/**
 * 获取所有可用的 SVG 图标名称
 * @returns {Array<string>} 图标名称数组
 */
export function getAvailableIcons() {
  // 使用 Vite 的 import.meta.glob 来动态导入所有 SVG 文件
  const svgModules = import.meta.glob('/src/assets/svg/*.svg', { 
    eager: true,
    as: 'url'
  })
  
  // 提取文件名（不含路径和扩展名）
  const iconNames = Object.keys(svgModules).map(path => {
    const fileName = path.split('/').pop() // 获取文件名
    return fileName.replace('.svg', '') // 移除 .svg 扩展名
  })
  
  // 按字母顺序排序
  return iconNames.sort()
}

/**
 * 检查指定的图标是否存在
 * @param {string} iconName 图标名称
 * @returns {boolean} 是否存在
 */
export function isIconExists(iconName) {
  if (!iconName) return false
  
  const availableIcons = getAvailableIcons()
  return availableIcons.includes(iconName)
}

/**
 * 获取图标的完整路径
 * @param {string} iconName 图标名称
 * @returns {string} 图标路径
 */
export function getIconPath(iconName) {
  return `/src/assets/svg/${iconName}.svg`
}

/**
 * 预定义的常用图标分类
 */
export const iconCategories = {
  // 导航类
  navigation: ['menu', 'home', 'dashboard', 'setting', 'user', 'search'],
  
  // 操作类
  actions: ['edit', 'delete', 'add', 'plus', 'minus', 'check', 'close', 'refresh'],
  
  // 箭头类
  arrows: ['arrow-up', 'arrow-down', 'arrow-left', 'arrow-right', 'enter'],
  
  // 文件类
  files: ['file', 'document', 'folder', 'image', 'video', 'audio', 'excel', 'zip'],
  
  // 系统类
  system: ['build', 'bug', 'code', 'component', 'dict', 'permission', 'lock'],
  
  // 表单类
  forms: ['form', 'button', 'checkbox', 'cascader', 'date', 'color', 'upload'],
  
  // 图表类
  charts: ['chart', 'table', 'tree', 'tree-table', 'list'],
  
  // 通信类
  communication: ['email', 'phone', 'message', 'qq', 'wechat'],
  
  // 其他
  others: ['star', 'heart', 'tag', 'bookmark', 'calendar', 'clock', 'location']
}

/**
 * 根据分类获取图标
 * @param {string} category 分类名称
 * @returns {Array<string>} 该分类下的图标列表
 */
export function getIconsByCategory(category) {
  return iconCategories[category] || []
}

/**
 * 搜索图标
 * @param {string} keyword 搜索关键词
 * @param {Array<string>} iconList 图标列表
 * @returns {Array<string>} 匹配的图标列表
 */
export function searchIcons(keyword, iconList) {
  if (!keyword) return iconList
  
  const lowerKeyword = keyword.toLowerCase()
  return iconList.filter(icon => 
    icon.toLowerCase().includes(lowerKeyword)
  )
}
