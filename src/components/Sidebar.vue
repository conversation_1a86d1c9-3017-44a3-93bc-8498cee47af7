<template>
  <div class="admin-sidebar">
    <!-- Logo区域 -->
    <div class="sidebar-logo">
      <div class="logo-container">
        <div class="logo-mini">A</div>
        <transition name="fade">
          <h1 v-if="!layoutStore.actualCollapsed" class="logo-title">a<PERSON>ey Admin</h1>
        </transition>
      </div>
    </div>
    
    <!-- 菜单区域 -->
    <div class="sidebar-menu">
      <el-menu
        :default-active="activeMenu"
        :collapse="layoutStore.actualCollapsed"
        :unique-opened="true"
        background-color="transparent"
        :text-color="'var(--text-color-inverse)'"
        :collapse-transition="false"
        :popper-offset="10"
        :active-text-color="'var(--text-color-white)'"
        class="sidebar-menu-el"
        mode="vertical"
        @select="handleMenuSelect"
      >
        <!-- 使用递归菜单组件 -->
        <MenuItem
          v-for="item in menuList"
          :key="item.path"
          :menu-item="item"
          :level="1"
        />
      </el-menu>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLayoutStore } from '@/stores/layout'
import { useMenuStore } from '@/stores/menu'
import MenuItem from './MenuItem.vue'

export default {
  name: 'Sidebar',
  components: {
    MenuItem
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const layoutStore = useLayoutStore()
    const menuStore = useMenuStore()

    // 过滤并处理菜单数据（保持树形结构）
    const processMenuData = (menus, parentPath = '') => {
      return menus.filter(menu => {
        // 只显示目录(1)和菜单(2)，排除按钮(3)
        if (menu.menuType === 3) {
          return false
        }

        // 只显示可见菜单
        if (menu.visible === 0) {
          return false
        }

        return true
      }).map(menu => {
        // 构建当前菜单的完整路径
        let currentPath = parentPath
        let menuPath = menu.routePath

        // 如果是目录类型(1)，需要将其路径添加到父路径中
        if (menu.menuType === 1 && menu.routePath) {
          let dirPath = menu.routePath
          if (dirPath.startsWith('/')) {
            dirPath = dirPath.substring(1)
          }
          currentPath = parentPath ? `${parentPath}/${dirPath}` : dirPath
          // 目录使用层级路径，但不是最终的路由路径
          menuPath = `/${currentPath}`
        }

        // 如果是菜单类型(2)，生成完整的层级路径
        if (menu.menuType === 2 && menu.routePath) {
          let routePath = menu.routePath
          if (routePath.startsWith('/')) {
            routePath = routePath.substring(1)
          }

          // 如果有父路径（来自目录），则组合路径
          const fullPath = currentPath ? `${currentPath}/${routePath}` : routePath
          menuPath = `/${fullPath}`
        }

        // 处理外部链接的特殊情况
        if (menu.externalLink === 1 && menu.openMode === '_self') {
          let linkPath = `link/${menu.id}`
          menuPath = currentPath ? `/${currentPath}/${linkPath}` : `/${linkPath}`
        }

        const processedMenu = {
          ...menu,
          path: menuPath, // 使用完整的层级路径
          title: menu.menuName,
          icon: menu.icon // 保留原始图标名称，用于svg-icon组件
        }

        // 递归处理子菜单，传递当前路径
        if (menu.children && menu.children.length > 0) {
          processedMenu.children = processMenuData(menu.children, currentPath)
        }

        return processedMenu
      })
    }

    // 动态菜单列表 - 直接使用后端的树形结构
    const menuList = computed(() => {
      console.log('菜单状态:', {
        isLoaded: menuStore.isLoaded,
        loading: menuStore.loading,
        menusLength: menuStore.menus.length,
        menus: menuStore.menus
      })

      // 详细显示菜单数据结构
      if (menuStore.menus.length > 0) {
        console.log('菜单数据示例:', menuStore.menus[0])
        menuStore.menus.forEach((menu, index) => {
          console.log(`菜单 ${index}:`, {
            menuName: menu.menuName,
            routePath: menu.routePath,
            componentPath: menu.componentPath,
            menuType: menu.menuType,
            visible: menu.visible,
            children: menu.children?.length || 0
          })
        })
      }

      // 默认仪表盘菜单项，始终存在
      const defaultDashboard = {
        path: '/dashboard',
        title: '仪表盘',
        icon: 'all-application', // 使用SVG图标名称
        menuType: 2
      }

      if (!menuStore.isLoaded || !menuStore.menus.length) {
        // 如果菜单未加载，只显示默认仪表盘
        console.log('使用默认菜单')
        return [defaultDashboard]
      }

      // 处理菜单数据，保持树形结构
      const processedMenus = processMenuData([...menuStore.menus], '')
      console.log('处理后的菜单:', processedMenus)

      // 将默认仪表盘添加到菜单列表的开头
      return [defaultDashboard, ...processedMenus]
    })

    // 当前激活的菜单
    const activeMenu = computed(() => {
      return route.path
    })

    // 处理菜单选择事件
    const handleMenuSelect = (menuPath) => {
      // 根据菜单路径找到对应的菜单项
      const findMenuByPath = (menus, path) => {
        for (const menu of menus) {
          if (menu.path === path) {
            return menu
          }
          if (menu.children && menu.children.length > 0) {
            const found = findMenuByPath(menu.children, path)
            if (found) return found
          }
        }
        return null
      }

      const menuItem = findMenuByPath(menuList.value, menuPath)
      if (!menuItem) return

      // 处理外部链接（新窗口打开）
      if (menuItem.externalLink === 1 && menuItem.openMode === '_blank') {
        // 使用原始外链地址打开新窗口
        window.open(menuItem.originalExternalUrl || menuItem.routePath, '_blank', 'noopener,noreferrer')
      } else {
        // 其他情况进行路由跳转
        router.push(menuPath)
      }

      // 移动端自动关闭侧边栏
      layoutStore.hideMobileSidebar()
    }

    return {
      layoutStore,
      menuStore,
      menuList,
      activeMenu,
      handleMenuSelect
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-color-sidebar);
}

.sidebar-logo {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    transform: translateX(-50%);
    opacity: 0.6;
  }

  .logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.05);

      .logo-mini {
        box-shadow:
          0 8px 25px rgba(59, 130, 246, 0.4),
          0 0 20px rgba(59, 130, 246, 0.3) inset;
        animation: pulse 2s infinite;
      }

      .logo-title {
        text-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
      }
    }

    .logo-img {
      width: 32px;
      height: 32px;
      border-radius: var(--border-radius-md);
    }

    .logo-mini {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      border-radius: var(--border-radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 16px;
      box-shadow:
        0 4px 15px rgba(59, 130, 246, 0.3),
        0 0 10px rgba(255, 255, 255, 0.2) inset;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent);
        border-radius: var(--border-radius-md);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover::before {
        opacity: 1;
      }
    }

    .logo-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color-white);
      margin: 0;
      white-space: nowrap;
      transition: all 0.3s ease;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
  }
}

// 菜单默认样式-未折叠状态
.menu-item {
  margin: 4px 8px;
  border-radius: 8px;
  height: 48px;
  line-height: 48px;
  overflow: hidden;

  &:hover {
    background: rgba(var(--primary-color-rgb), 0.1) !important;

    .menu-icon,
    .menu-title {
      color: var(--menu-item-color-hover) !important;
    }
  }

  &.is-active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
    box-shadow: var(--box-shadow-lg);

    .menu-icon,
    .menu-title {
      color: var(--text-color-white) !important;
    }
  }
}


.sub-menu {
  margin: 4px 8px;
  border-radius: 8px;
  overflow: hidden;
  :deep(.el-sub-menu__title) {
    height: 48px;
    line-height: 48px;
    border-radius: 8px;
    &:hover {
      background: rgba(var(--primary-color-rgb), 0.1) !important;

      .menu-icon,
      .menu-title {
        color: var(--menu-item-color-hover) !important;
      }
    }
  }

  :deep(.el-menu-item) {
    height: 44px;
    line-height: 44px;
    border-radius: 6px;

    &:hover {
      background: rgba(var(--primary-color-rgb), 0.1) !important;

      .menu-icon,
      .menu-title {
        color: var(--menu-item-color-hover) !important;
      }
    }

    &.is-active {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
      box-shadow: var(--box-shadow-lg);
      color: var(--text-color-white) !important;

      .menu-icon,
      .menu-title {
        color: var(--text-color-white) !important;
      }
    }
  }
}

// 菜单折叠后的样式
.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;

  .sidebar-menu-el {
    border: none;

    // 折叠状态下的样式
    &.el-menu--collapse {
      // 一级菜单（没有子菜单的菜单项）
      :deep(.menu-item.el-menu-item) {
        margin: 4px 8px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        padding: 0 !important;
        width: calc(100% - 16px) !important; // 减去左右边距

        .menu-icon {
          margin-right: 0 !important;
          margin-left: 0 !important;
          font-size: 24px !important; // 折叠时图标更大
          width: 24px !important;
          height: 24px !important;

          // 确保SVG图标也应用正确尺寸
          svg {
            width: 24px !important;
            height: 24px !important;
          }

          // Element Plus图标尺寸
          .el-icon {
            font-size: 24px !important;
          }
        }

        // 隐藏菜单标题
        .menu-title {
          display: none !important;
        }

        // 修复tooltip触发器样式，保证图标正常显示和tooltip正常工作
        .el-menu-tooltip__trigger {
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          width: 100% !important;
          height: 100% !important;
        }
      }

      // 子菜单
      .sub-menu {
        margin: 4px 8px !important; // 保持左右边距

        :deep(.el-sub-menu__title) {
          display: flex !important;
          justify-content: center !important;
          align-items: center !important;
          padding: 0 !important;
          width: 100% !important;

          .menu-icon {
            margin-right: 0 !important;
            margin-left: 0 !important;
            font-size: 24px !important; // 折叠时图标更大
            width: 24px !important;
            height: 24px !important;

            // 确保SVG图标也应用正确尺寸
            svg {
              width: 24px !important;
              height: 24px !important;
            }

            // Element Plus图标尺寸
            .el-icon {
              font-size: 24px !important;
            }
          }

          // 隐藏菜单标题
          .menu-title {
            display: none !important;
          }

          // 隐藏箭头图标
          .el-sub-menu__icon-arrow {
            display: none !important;
          }

          // 修复子菜单tooltip触发器样式
          .el-menu-tooltip__trigger {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            width: 100% !important;
            height: 100% !important;
          }
        }
      }
    }
  }
}


// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}


// Logo脉冲效果（保留）
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

// 响应式设计
// 移动端 (< 768px)
@media (max-width: 767px) {
  .admin-sidebar {
    width: 280px !important;

    .sidebar-logo {
      padding: 0 16px;

      .logo-container {
        .logo-mini {
          width: 36px;
          height: 36px;
          font-size: 18px;
        }

        .logo-title {
          font-size: 16px;
          display: block !important; // 移动端强制显示Logo标题
        }
      }
    }

    .sidebar-menu {
      padding: 12px 0;

      .sidebar-menu-el {
        // 移动端菜单项样式调整
        :deep(.menu-item) {
          margin: 2px 8px;
          height: 44px;
          line-height: 44px;

          .menu-icon {
            font-size: 16px;
          }

          .menu-title {
            font-size: 13px;
          }
        }

        :deep(.sub-menu) {
          margin: 2px 8px;

          .el-sub-menu__title {
            height: 44px;
            line-height: 44px;

            .menu-icon {
              font-size: 16px;
            }

            .menu-title {
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}



// 平板端 (768px - 1023px)
@media (min-width: 768px) and (max-width: 1023px) {
  .admin-sidebar {
    // 平板端默认使用折叠模式
    .sidebar-logo {
      .logo-container {
        .logo-title {
          display: none !important; // 平板端强制隐藏标题
        }
      }
    }

    .sidebar-menu {
      .sidebar-menu-el {
        // 平板端折叠样式
        &.el-menu--collapse {
          .menu-item {
            margin: 4px 0;

            :deep(.el-menu-item) {
              padding: 0 !important;
              justify-content: center;
            }
          }

          .sub-menu {
            margin: 4px 0;

            :deep(.el-sub-menu__title) {
              padding: 0 !important;
              justify-content: center;
            }
          }
        }
      }
    }
  }
}

// 桌面端 (>= 1024px)
@media (min-width: 1024px) {
  .admin-sidebar {
    .sidebar-logo {
      padding: 0 20px;
    }

    .sidebar-menu {
      padding: 16px 0;
    }
  }
}
</style>
