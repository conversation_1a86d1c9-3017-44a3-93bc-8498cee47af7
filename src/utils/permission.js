import { usePermissionStore } from '@/stores/permission'

/**
 * 权限校验工具类
 */
class PermissionChecker {
  constructor() {
    this.permissionStore = null
  }

  /**
   * 获取权限store实例
   * @returns {Object}
   */
  getPermissionStore() {
    if (!this.permissionStore) {
      this.permissionStore = usePermissionStore()
    }
    return this.permissionStore
  }

  /**
   * 检查是否有指定权限
   * @param {string} permission 权限字符串
   * @returns {boolean}
   */
  hasPermission(permission) {
    const store = this.getPermissionStore()
    return store.hasPermission(permission)
  }

  /**
   * 检查是否有任意一个权限（OR逻辑）
   * @param {Array<string>|string} permissions 权限数组或单个权限字符串
   * @returns {boolean}
   */
  hasAnyPermission(permissions) {
    const store = this.getPermissionStore()
    
    if (typeof permissions === 'string') {
      return store.hasPermission(permissions)
    }
    
    if (Array.isArray(permissions)) {
      return store.hasAnyPermission(permissions)
    }
    
    return false
  }

  /**
   * 检查是否有所有权限（AND逻辑）
   * @param {Array<string>|string} permissions 权限数组或单个权限字符串
   * @returns {boolean}
   */
  hasAllPermissions(permissions) {
    const store = this.getPermissionStore()
    
    if (typeof permissions === 'string') {
      return store.hasPermission(permissions)
    }
    
    if (Array.isArray(permissions)) {
      return store.hasAllPermissions(permissions)
    }
    
    return false
  }

  /**
   * 检查路由权限
   * @param {Object} route 路由对象
   * @returns {boolean}
   */
  checkRoutePermission(route) {
    // 检查路由及其父路由的权限要求
    const matched = route.matched || []
    
    for (const record of matched) {
      const meta = record.meta || {}
      
      // 检查单个权限
      if (meta.permission && !this.hasPermission(meta.permission)) {
        return false
      }
      
      // 检查权限数组（需要所有权限）
      if (meta.permissions && !this.hasAllPermissions(meta.permissions)) {
        return false
      }
      
      // 检查权限数组（需要任意一个权限）
      if (meta.permissionsOr && !this.hasAnyPermission(meta.permissionsOr)) {
        return false
      }
    }
    
    return true
  }

  /**
   * 获取当前用户的所有权限
   * @returns {Array<string>}
   */
  getPermissions() {
    const store = this.getPermissionStore()
    return store.permissions
  }
}

// 创建单例实例
const permissionChecker = new PermissionChecker()

// 导出便捷方法
export const hasPermission = (permission) => permissionChecker.hasPermission(permission)
export const hasAnyPermission = (permissions) => permissionChecker.hasAnyPermission(permissions)
export const hasAllPermissions = (permissions) => permissionChecker.hasAllPermissions(permissions)
export const checkRoutePermission = (route) => permissionChecker.checkRoutePermission(route)
export const getPermissions = () => permissionChecker.getPermissions()

// 导出类和实例
export { PermissionChecker }
export default permissionChecker
