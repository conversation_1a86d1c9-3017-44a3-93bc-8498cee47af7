<template>
  <el-dialog
    v-model="dialogVisible"
    title="新增用户"
    :width="dialogWidth"
    :close-on-click-modal="false"
    append-to-body
    class="user-add-dialog"
    @close="handleClose"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :label-width="labelWidth"
        :label-position="labelPosition"
        @submit.prevent
      >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="form.username"
          placeholder="请输入用户名"
          maxlength="50"
          show-word-limit
          @keyup.enter="handleSubmit"
        />
      </el-form-item>
      
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
          maxlength="100"
          show-password
          show-word-limit
          @keyup.enter="handleSubmit"
        />
      </el-form-item>

      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="form.confirmPassword"
          type="password"
          placeholder="请再次输入密码"
          maxlength="100"
          show-password
          @keyup.enter="handleSubmit"
        />
      </el-form-item>

      <el-form-item label="用户类型" prop="userTypeId">
        <el-select
          v-model="form.userTypeId"
          placeholder="请选择用户类型"
          style="width: 100%"
        >
          <el-option
            v-for="type in userTypes"
            :key="type.id"
            :label="type.typeName"
            :value="type.id"
          />
        </el-select>
      </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { createUser } from '@/api/user'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userTypes: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const isMobile = ref(false)

// 表单数据
const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  userTypeId: ''
})

// 确认密码验证函数
const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 50, message: '用户名长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 100, message: '密码长度在 6 到 100 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ],
  userTypeId: [
    { required: true, message: '请选择用户类型', trigger: 'change' }
  ]
}

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogWidth = computed(() => {
  return isMobile.value ? '90%' : '500px'
})

const labelWidth = computed(() => {
  return isMobile.value ? '80px' : '100px'
})

const labelPosition = computed(() => {
  return isMobile.value ? 'top' : 'right'
})

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      initForm()
    }
  },
  { immediate: true }
)

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听密码变化，重新验证确认密码
watch(() => form.password, () => {
  if (form.confirmPassword && formRef.value) {
    formRef.value.validateField('confirmPassword')
  }
})

// 初始化表单
const initForm = () => {
  nextTick(() => {
    // 重置表单数据
    Object.assign(form, {
      username: '',
      password: '',
      confirmPassword: '',
      userTypeId: ''
    })

    // 清除验证状态
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 构建提交数据，排除确认密码字段
    const submitData = {
      username: form.username,
      password: form.password,
      userTypeId: form.userTypeId
    }

    await createUser(submitData)
    ElMessage.success('新增用户成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('新增用户失败:', error)
    ElMessage.error('新增用户失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
/* 对话框内容区域 */
.dialog-content {
  padding: 20px 0;
}

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dialog-content {
    padding: 16px 0;
  }

  .dialog-footer {
    padding-top: 16px;
    gap: 8px;
  }

  .dialog-footer .el-button {
    flex: 1;
    min-width: 80px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .dialog-content {
    padding: 12px 0;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .dialog-footer .el-button {
    width: 100%;
  }
}
</style>

<!-- 全局样式 -->
<style>
/* 对话框整体样式优化 */
.user-add-dialog .el-dialog {
  margin: 15vh auto 50px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.user-add-dialog .el-dialog__header {
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.user-add-dialog .el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.user-add-dialog .el-dialog__body {
  padding: 0 20px;
}

.user-add-dialog .el-dialog__footer {
  padding: 0 20px 20px 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 表单样式优化 */
.user-add-dialog .el-form-item {
  margin-bottom: 20px;
}

.user-add-dialog .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 移动端对话框适配 */
@media (max-width: 768px) {
  .user-add-dialog .el-dialog {
    margin: 10vh auto 20px;
    width: 90% !important;
    max-width: none;
  }

  .user-add-dialog .el-dialog__header {
    padding: 16px 16px 0 16px;
  }

  .user-add-dialog .el-dialog__body {
    padding: 0 16px;
  }

  .user-add-dialog .el-dialog__footer {
    padding: 0 16px 16px 16px;
  }

  .user-add-dialog .el-form-item {
    margin-bottom: 16px;
  }
}

/* 超小屏幕对话框适配 */
@media (max-width: 480px) {
  .user-add-dialog .el-dialog {
    margin: 5vh auto 10px;
    width: 95% !important;
  }

  .user-add-dialog .el-dialog__header {
    padding: 12px 12px 0 12px;
  }

  .user-add-dialog .el-dialog__body {
    padding: 0 12px;
  }

  .user-add-dialog .el-dialog__footer {
    padding: 0 12px 12px 12px;
  }

  .user-add-dialog .el-form-item {
    margin-bottom: 12px;
  }

  .user-add-dialog .el-form-item__label {
    font-size: 14px;
    margin-bottom: 8px;
  }
}
</style>
