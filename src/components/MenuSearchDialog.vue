<template>
  <el-dialog
    v-model="visible"
    title="菜单搜索"
    width="700px"
    :close-on-click-modal="false"
    class="menu-search-dialog"
    append-to-body
    @closed="handleClosed"
  >
    <div class="menu-search-content">
      <!-- 搜索输入框 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="输入菜单名称或路由地址进行搜索..."
          clearable
          size="large"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 菜单列表 -->
      <div class="menu-list-section">
        <div v-if="filteredMenus.length === 0" class="empty-state">
          <el-empty description="未找到匹配的菜单" />
        </div>
        <div v-else class="menu-list">
          <div
            v-for="menu in filteredMenus"
            :key="menu.id"
            class="menu-item"
            @click="handleMenuSelect(menu)"
          >
            <div class="menu-icon">
              <template v-if="menu.icon">
                <svg-icon
                  :name="menu.icon"
                  class="icon"
                />
              </template>

              <template v-else>
                <el-icon class="default-icon">
                  <Document />
                </el-icon>
              </template>
            </div>
            <div class="menu-info">
              <div class="menu-name">{{ menu.menuName }}</div>
              <div class="menu-route">{{ menu.routePath }}</div>
            </div>
            <div class="menu-action">
              <el-icon class="arrow-icon">
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useMenuStore } from '@/stores/menu'
import { ElMessage } from 'element-plus'
import { Search, Document, ArrowRight } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'menu-select'])

// 响应式数据
const router = useRouter()
const menuStore = useMenuStore()
const searchKeyword = ref('')

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 扁平化菜单数据，用于搜索
const flattenMenus = (menus) => {
  const result = []
  const traverse = (items) => {
    items.forEach(item => {
      // 只添加菜单类型的项目（menuType === 2）且有路由路径的
      if (item.menuType === 2 && item.routePath && item.routePath !== '#') {
        result.push({
          menuName: item.menuName,
          routePath: item.routePath,
          icon: item.icon,
          id: item.id,
          externalLink: item.externalLink,
          openMode: item.openMode
        })
      }
      // 递归处理子菜单
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }
  traverse(menus)
  return result
}

// 计算属性：过滤后的菜单列表
const filteredMenus = computed(() => {
  // 默认仪表盘菜单项，始终存在
  const defaultDashboard = {
    menuName: '仪表盘',
    routePath: '/dashboard',
    icon: 'dashboard',
    id: 'default-dashboard'
  }

  // 获取后端菜单数据
  const flatMenus = flattenMenus(menuStore.menus)

  // 合并默认仪表盘和后端菜单
  const allMenus = [defaultDashboard, ...flatMenus]

  if (!searchKeyword.value.trim()) {
    return allMenus
  }

  return allMenus.filter(menu => {
    return menu.menuName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
           menu.routePath.toLowerCase().includes(searchKeyword.value.toLowerCase())
  })
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleMenuSelect = (item) => {
  if (!item.routePath) {
    return
  }
  debugger
  // 处理外部链接（新窗口打开）
  if (item.externalLink === 1 && item.openMode === '_blank') {
    // 使用原始外链地址
    window.open(item.routePath, '_blank', 'noopener,noreferrer')
    ElMessage.success(`已在新窗口打开：${item.menuName}`)
  } else {
    // 其他情况（内部路由和当前窗口外部链接）直接使用处理后的路径
    router.push(item.routePath)
    ElMessage.success(`跳转到：${item.menuName}`)
  }

  visible.value = false
  searchKeyword.value = ''
  emit('menu-select', item)
}

const handleClosed = () => {
  searchKeyword.value = ''
}

// 监听弹窗打开，重置搜索关键词
watch(visible, (newValue) => {
  if (newValue) {
    searchKeyword.value = ''
  }
})
</script>

<style lang="scss" scoped>
// 菜单搜索弹窗样式
:deep(.menu-search-dialog) {
  .el-dialog__header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color-light);
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color-primary);
    }
  }

  .el-dialog__body {
    padding: 0;
  }
}

.menu-search-content {
  .search-section {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color-light);


    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--primary-color);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        &.is-focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
      }

      .el-input__inner {
        font-size: 16px;
        padding: 12px 16px;
      }

      .el-input__prefix {
        color: var(--text-color-secondary);
      }
    }
  }

  .menu-list-section {
    max-height: 400px;
    overflow-y: auto;
    padding: 12px 0;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--bg-color-page);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--border-color);
      border-radius: 3px;

      &:hover {
        background: var(--primary-color);
      }
    }

    .empty-state {
      padding: 40px 24px;
      text-align: center;
    }

    .menu-list {
      padding: 0 12px;

      .menu-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        margin: 4px 0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid transparent;

        &:hover {
          background: var(--bg-color-hover);
          border-color: var(--primary-color);
          transform: translateX(4px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);

          .menu-icon .icon {
            color: var(--primary-color);
            transform: scale(1.1);
          }

          .menu-info .menu-name {
            color: var(--primary-color);
            font-weight: 600;
          }

          .menu-action .arrow-icon {
            color: var(--primary-color);
            transform: translateX(4px);
          }
        }

        .menu-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: var(--bg-color-page);
          border-radius: 8px;
          margin-right: 16px;
          flex-shrink: 0;

          .icon {
            width: 20px;
            height: 20px;
            color: var(--primary-color);
            transition: all 0.3s ease;
          }

          .default-icon {
            font-size: 20px;
            color: var(--text-color-placeholder);
            transition: all 0.3s ease;
          }
        }

        .menu-info {
          flex: 1;
          min-width: 0;

          .menu-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color-primary);
            line-height: 1.5;
            margin-bottom: 4px;
            transition: all 0.3s ease;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .menu-route {
            font-size: 12px;
            color: var(--text-color-placeholder);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: var(--bg-color-page);
            padding: 2px 8px;
            border-radius: 4px;
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            max-width: 100%;
            border: 1px solid var(--border-color-lighter);
          }
        }

        .menu-action {
          margin-left: 12px;
          flex-shrink: 0;

          .arrow-icon {
            font-size: 16px;
            color: var(--text-color-placeholder);
            transition: all 0.3s ease;
          }
        }
      }
    }
  }
}
</style>
