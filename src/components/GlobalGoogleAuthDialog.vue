<template>
  <!-- 自定义弹窗遮罩层 -->
  <teleport to="body">
    <transition name="modal-fade">
      <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
        <transition name="modal-slide">
          <div v-if="visible" class="modal-container" @click.stop>
            <div class="auth-container">
              <!-- 头部区域 -->
              <div class="auth-header">
                <div class="auth-icon-wrapper">
                  <div class="auth-icon">
                    <svg viewBox="0 0 24 24" width="28" height="28">
                      <path fill="#4285f4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34a853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#fbbc05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#ea4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                  </div>
                </div>
                <h2 class="auth-title">安全验证</h2>
                <p class="auth-subtitle">请输入谷歌验证器中的6位验证码</p>
              </div>

              <!-- 输入区域 -->
              <div class="auth-input-section">
                <div class="input-wrapper">
                  <VerificationCodeInput
                    ref="codeInputRef"
                    v-model="authCode"
                    :auto-submit="true"
                    :disabled="loading"
                    @complete="handleCodeComplete"
                    @submit="handleSubmit"
                  />
                </div>

                <!-- 错误提示 -->
                <transition name="error-fade">
                  <div v-if="errorMessage" class="error-message">
                    <svg class="error-icon" viewBox="0 0 20 20" width="16" height="16">
                      <path fill="currentColor" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
                    </svg>
                    <span>{{ errorMessage }}</span>
                  </div>
                </transition>
              </div>

              <!-- 按钮区域 -->
              <div class="auth-actions">
                <button
                  class="btn btn-cancel"
                  :disabled="loading"
                  @click="handleCancel"
                >
                  取消
                </button>
                <button
                  class="btn btn-confirm"
                  :disabled="authCode.length !== 6 || loading"
                  @click="handleSubmit"
                >
                  <span v-if="loading" class="loading-spinner"></span>
                  <span>{{ loading ? '验证中...' : '确认验证' }}</span>
                </button>
              </div>

              <!-- 帮助信息 -->
              <div class="auth-help">
                <div class="help-item">
                  <svg class="help-icon" viewBox="0 0 20 20" width="14" height="14">
                    <path fill="currentColor" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"/>
                  </svg>
                  <span>打开谷歌验证器应用获取验证码</span>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </teleport>
</template>

<script>
import { ref, nextTick, watch } from 'vue'
import VerificationCodeInput from './VerificationCodeInput.vue'

export default {
  name: 'GlobalGoogleAuthDialog',
  components: {
    VerificationCodeInput
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const visible = ref(false)
    const authCode = ref('')
    const loading = ref(false)
    const errorMessage = ref('')
    const codeInputRef = ref()

    // 显示弹窗
    const show = () => {
      visible.value = true
      authCode.value = ''
      errorMessage.value = ''
      loading.value = false
      
      // 自动聚焦到验证码输入框
      nextTick(() => {
        if (codeInputRef.value) {
          codeInputRef.value.focus()
        }
      })
    }

    // 隐藏弹窗
    const hide = () => {
      visible.value = false
      authCode.value = ''
      errorMessage.value = ''
      loading.value = false
    }

    // 设置错误信息
    const setError = (message) => {
      errorMessage.value = message
      authCode.value = ''
      loading.value = false
      
      // 清空输入框并重新聚焦
      if (codeInputRef.value) {
        codeInputRef.value.clear()
        nextTick(() => {
          codeInputRef.value.focus()
        })
      }
    }

    // 设置加载状态
    const setLoading = (isLoading) => {
      loading.value = isLoading
    }

    // 验证码输入完成
    const handleCodeComplete = (code) => {
      console.log('谷歌验证码输入完成:', code)
    }

    // 提交验证码
    const handleSubmit = () => {
      if (authCode.value.length !== 6) {
        setError('请输入完整的6位验证码')
        return
      }

      loading.value = true
      errorMessage.value = ''
      
      emit('submit', authCode.value)
    }

    // 取消验证
    const handleCancel = () => {
      emit('cancel')
      hide()
    }

    // 处理遮罩层点击
    const handleOverlayClick = () => {
      // 不允许点击遮罩层关闭弹窗，保持安全性
    }

    // 监听弹窗显示状态，自动聚焦
    watch(visible, (newVal) => {
      if (newVal) {
        nextTick(() => {
          if (codeInputRef.value) {
            codeInputRef.value.focus()
          }
        })
      }
    })

    return {
      visible,
      authCode,
      loading,
      errorMessage,
      codeInputRef,
      show,
      hide,
      setError,
      setLoading,
      handleCodeComplete,
      handleSubmit,
      handleCancel,
      handleOverlayClick
    }
  }
}
</script>

<style lang="scss" scoped>
// 弹窗遮罩层
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 9001;
  padding: var(--spacing-lg);
  padding-top: 20vh; /* 让弹窗往上偏移更多 */
}

// 弹窗容器
.modal-container {
  position: relative;
  width: 100%;
  max-width: 460px;
  background: var(--bg-color-card);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid var(--border-color-light);
  overflow: hidden;
}



.auth-container {
  padding: 30px;
  text-align: center;
}

.auth-header {
  margin-bottom: 32px;

  .auth-icon-wrapper {
    margin-bottom: 20px;

    .auth-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      border-radius: var(--border-radius-xl);
      box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.3);

      svg {
        filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.2));
      }
    }
  }

  .auth-title {
    margin: 0 0 12px 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.3px;
  }

  .auth-subtitle {
    margin: 0;
    font-size: var(--font-size-md);
    color: var(--text-color-secondary);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
  }
}

.auth-input-section {
  margin-bottom: 32px;

  .input-wrapper {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--danger-lightest);
  border: 1px solid var(--danger-lighter);
  border-radius: var(--border-radius-lg);
  color: var(--danger-color);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);

  .error-icon {
    flex-shrink: 0;
  }
}

.error-fade-enter-active,
.error-fade-leave-active {
  transition: all var(--transition-normal);
}

.error-fade-enter-from,
.error-fade-leave-to {
  opacity: 0;
  transform: translateY(-8px);
}

.auth-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;

  .btn {
    flex: 1;
    height: 52px;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--border-radius-lg);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-family: inherit;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }

    &.btn-cancel {
      background: var(--bg-color-hover);
      color: var(--text-color-regular);
      border: 1px solid var(--border-color);

      &:hover:not(:disabled) {
        background: var(--border-color-light);
        border-color: var(--border-color-dark);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    &.btn-confirm {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: var(--text-color-white);
      box-shadow: 0 4px 15px rgba(var(--primary-color-rgb), 0.4);

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(var(--primary-color-rgb), 0.5);
      }

      &:active:not(:disabled) {
        transform: translateY(0);
      }
    }
  }
}

// 加载动画
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 弹窗动画
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity var(--transition-normal);
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-slide-enter-active,
.modal-slide-leave-active {
  transition: all var(--transition-normal);
}

.modal-slide-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(-20px);
}

.modal-slide-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.auth-help {
  .help-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    color: var(--text-color-secondary);
    font-size: var(--font-size-sm);

    .help-icon {
      flex-shrink: 0;
    }
  }
}

// 暗黑模式适配
[data-theme="dark"] {
  .modal-overlay {
    background: rgba(0, 0, 0, 0.7);
  }

  .modal-container {
    background: var(--bg-color-card);
    border-color: var(--border-color);
  }



  .auth-header {
    .auth-title {
      background: linear-gradient(135deg, var(--primary-light), var(--primary-lighter));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .auth-subtitle {
      color: var(--text-color-secondary);
    }
  }

  .error-message {
    background: var(--danger-lightest);
    border-color: var(--border-color);
    color: var(--danger-light);
  }

  .auth-actions {
    .btn-cancel {
      background: var(--bg-color-hover);
      color: var(--text-color-regular);
      border-color: var(--border-color);

      &:hover:not(:disabled) {
        background: var(--border-color-light);
        border-color: var(--border-color-dark);
      }
    }
  }

  .help-item {
    color: var(--text-color-secondary);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .modal-container {
    max-width: 400px;
    border-radius: var(--border-radius-lg);
  }

  .auth-container {
    padding: 24px;
  }

  .auth-header {
    margin-bottom: 28px;

    .auth-icon-wrapper .auth-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-lg);
    }

    .auth-title {
      font-size: var(--font-size-xl);
    }

    .auth-subtitle {
      font-size: var(--font-size-sm);
    }
  }

  .auth-input-section {
    margin-bottom: 28px;
  }

  .auth-actions {
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;

    .btn {
      height: 64px;
      font-size: var(--font-size-md);
      width: 100%;
    }
  }

  .auth-help .help-item {
    font-size: var(--font-size-xs);
  }
}

// 超小屏幕适配
@media (max-width: 480px) {

  .modal-container {
    border-radius: var(--border-radius-md);
  }

  .auth-container {
    padding: 20px;
  }

  .auth-header {
    margin-bottom: 24px;

    .auth-icon-wrapper .auth-icon {
      width: 44px;
      height: 44px;
      border-radius: var(--border-radius-md);
    }

    .auth-title {
      font-size: var(--font-size-lg);
    }

    .auth-subtitle {
      font-size: var(--font-size-xs);
      line-height: var(--line-height-normal);
    }
  }

  .auth-actions {
    gap: 16px;

    .btn {
      height: 60px;
      font-size: var(--font-size-base);
    }
  }
}

// 横屏手机适配
@media (max-height: 600px) and (orientation: landscape) {
  .modal-overlay {
    align-items: flex-start;
    padding-top: var(--spacing-md);
  }

  .modal-container {
    max-height: 90vh;
    overflow-y: auto;
  }

  .auth-container {
    padding: 20px 30px;
  }

  .auth-header {
    margin-bottom: var(--spacing-md);

    .auth-icon-wrapper {
      margin-bottom: var(--spacing-sm);

      .auth-icon {
        width: 40px;
        height: 40px;
      }
    }

    .auth-title {
      font-size: var(--font-size-lg);
      margin-bottom: var(--spacing-xs);
    }

    .auth-subtitle {
      font-size: var(--font-size-xs);
    }
  }

  .auth-input-section {
    margin-bottom: var(--spacing-md);
  }

  .auth-actions {
    margin-bottom: var(--spacing-sm);

    .btn {
      height: 56px;
    }
  }
}

// 键盘弹起时的适配（移动端）
@media (max-height: 500px) {
  .modal-overlay {
    align-items: flex-start;
    padding-top: var(--spacing-sm);
  }

  .auth-container {
    padding: 16px;
  }

  .auth-header {
    margin-bottom: var(--spacing-sm);

    .auth-icon-wrapper {
      margin-bottom: var(--spacing-xs);

      .auth-icon {
        width: 36px;
        height: 36px;
      }
    }

    .auth-title {
      font-size: var(--font-size-md);
      margin-bottom: var(--spacing-xs);
    }

    .auth-subtitle {
      font-size: var(--font-size-xs);
    }
  }

  .auth-input-section {
    margin-bottom: var(--spacing-sm);
  }

  .auth-actions {
    margin-bottom: var(--spacing-xs);

    .btn {
      height: 52px;
      font-size: var(--font-size-sm);
    }
  }

  .auth-help {
    display: none; // 在极小屏幕上隐藏帮助信息
  }
}
</style>
