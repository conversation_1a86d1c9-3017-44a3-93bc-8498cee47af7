<template>
  <div class="management-page">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="客户端IP">
          <el-input
            v-model="searchForm.clientIp"
            placeholder="请输入IP地址"
            clearable
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="登录状态">
          <el-select
            v-model="searchForm.loginStatus"
            placeholder="请选择状态"
            clearable
            class="search-select"
          >
            <el-option label="成功" :value="1" />
            <el-option label="失败" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否可疑">
          <el-select
            v-model="searchForm.isSuspicious"
            placeholder="请选择"
            clearable
            class="search-select"
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="风险等级">
          <el-select
            v-model="searchForm.riskLevel"
            placeholder="请选择等级"
            clearable
            class="search-select"
          >
            <el-option label="低风险" value="LOW" />
            <el-option label="中风险" value="MEDIUM" />
            <el-option label="高风险" value="HIGH" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            :editable="false"
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-range-picker"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button
          type="warning"
          @click="handleCleanup"
        >
          <el-icon><Delete /></el-icon>
          清理
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ background: 'var(--bg-color-table-header)', color: 'var(--text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '56px' }"
      >
        <el-table-column prop="username" label="用户名" min-width="120" align="center" header-align="center">
          <template #default="{ row }">
            <div class="user-info">
              <span class="username">{{ row.username }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="loginTime" label="登录时间" min-width="160" align="center" header-align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.loginTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="loginStatus" label="登录状态" min-width="120" align="center" header-align="center">
          <template #default="{ row }">
            <div>
              <el-tag :type="row.loginStatus === 1 ? 'success' : 'danger'" size="small">
                {{ row.loginStatus === 1 ? '成功' : '失败' }}
              </el-tag>
              <div v-if="row.loginStatus === 0 && row.failureReason" class="failure-reason">
                {{ row.failureReason }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="clientIp" label="客户端IP" min-width="130" align="center" header-align="center">
          <template #default="{ row }">
            <span class="ip-address">{{ row.clientIp }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="登录地点" min-width="150" align="center" header-align="center">
          <template #default="{ row }">
            <span v-if="row.location">
              {{ row.location }}
            </span>
            <span v-else class="text-placeholder">未知</span>
          </template>
        </el-table-column>
        <el-table-column prop="deviceType" label="设备类型" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag v-if="row.deviceType" type="info" size="small">
              {{ row.deviceType }}
            </el-tag>
            <span v-else class="text-placeholder">未知</span>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag
              v-if="row.riskLevel"
              :type="getRiskLevelType(row.riskLevel)"
              size="small"
            >
              {{ getRiskLevelText(row.riskLevel) }}
            </el-tag>
            <span v-else class="text-placeholder">正常</span>
          </template>
        </el-table-column>
        <el-table-column prop="isSuspicious" label="可疑标记" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag v-if="row.isSuspicious === 1" type="warning" size="small">
              可疑
            </el-tag>
            <span v-else class="text-placeholder">正常</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" header-align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.size"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 登录日志详情对话框 -->
    <LoginLogDetailDialog
      v-model:visible="detailDialogVisible"
      :log-data="currentLog"
      @view-user-logs="handleViewUserLogs"
      @view-ip-logs="handleViewIpLogs"
    />

    <!-- 清理日志对话框 -->
    <CleanupLogsDialog
      v-model:visible="cleanupDialogVisible"
      @success="handleCleanupSuccess"
    />


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Delete
} from '@element-plus/icons-vue'
import {
  getLoginLogPage
} from '@/api/loginLog'
import { formatDateTime } from '@/utils/date'
import LoginLogDetailDialog from './components/LoginLogDetailDialog.vue'
import CleanupLogsDialog from './components/CleanupLogsDialog.vue'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const detailDialogVisible = ref(false)
const cleanupDialogVisible = ref(false)
const currentLog = ref({})
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  clientIp: '',
  loginStatus: null,
  isSuspicious: null,
  riskLevel: '',
  startTime: '',
  endTime: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 获取登录日志列表
const fetchLoginLogs = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await getLoginLogPage(params)
    tableData.value = response.records || []
    pagination.total = response.total || 0
  } catch (error) {
    console.error('获取登录日志列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchLoginLogs()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    clientIp: '',
    loginStatus: null,
    isSuspicious: null,
    riskLevel: '',
    startTime: '',
    endTime: ''
  })
  dateRange.value = []
  pagination.current = 1
  fetchLoginLogs()
}

// 时间范围变化
const handleDateRangeChange = (value) => {
  if (value && value.length === 2) {
    searchForm.startTime = value[0]
    searchForm.endTime = value[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

// 查看详情
const handleViewDetail = (row) => {
  currentLog.value = { ...row }
  detailDialogVisible.value = true
}




// 清理过期日志
const handleCleanup = () => {
  cleanupDialogVisible.value = true
}

// 清理成功回调
const handleCleanupSuccess = () => {
  cleanupDialogVisible.value = false
  fetchLoginLogs()
}



// 查看该用户日志
const handleViewUserLogs = (username) => {
  // 重置搜索表单
  Object.assign(searchForm, {
    username: username,
    clientIp: '',
    loginStatus: null,
    isSuspicious: null,
    riskLevel: '',
    startTime: '',
    endTime: ''
  })
  dateRange.value = []

  // 执行搜索
  pagination.current = 1
  fetchLoginLogs()

  ElMessage.success(`已筛选用户"${username}"的登录日志`)
}

// 查看该IP日志
const handleViewIpLogs = (clientIp) => {
  // 重置搜索表单
  Object.assign(searchForm, {
    username: '',
    clientIp: clientIp,
    loginStatus: null,
    isSuspicious: null,
    riskLevel: '',
    startTime: '',
    endTime: ''
  })
  dateRange.value = []

  // 执行搜索
  pagination.current = 1
  fetchLoginLogs()

  ElMessage.success(`已筛选IP"${clientIp}"的登录日志`)
}

// 获取风险等级类型
const getRiskLevelType = (level) => {
  const typeMap = {
    'LOW': 'success',
    'MEDIUM': 'warning',
    'HIGH': 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取风险等级文本
const getRiskLevelText = (level) => {
  const textMap = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高'
  }
  return textMap[level] || '未知'
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchLoginLogs()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchLoginLogs()
}

// 初始化
onMounted(() => {
  fetchLoginLogs()
})
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.username {
  font-weight: 500;
  color: var(--text-color-primary);
}

.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: var(--text-color-primary);
}

.text-placeholder {
  color: var(--text-color-placeholder);
  font-style: italic;
}

.failure-reason {
  font-size: 12px;
  color: var(--danger-color);
  margin-top: 4px;
  line-height: 1.2;
}

/* 搜索表单控件样式 */
.search-input {
  width: 200px;
}

.search-select {
  width: 120px;
}

/* 时间范围选择器响应式样式 */
.date-range-picker {
  width: 350px;
}

/* 页面特定样式 - 移动端适配已提取到公共样式文件 */
</style>
