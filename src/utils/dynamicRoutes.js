/**
 * 动态路由生成器
 * 根据后端菜单数据自动生成路由配置
 */

import { getComponentImport as getComponentImportFromConfig } from '@/config/componentMap'

/**
 * 根据后端返回的组件路径获取对应的组件导入函数
 * @param {string} componentPath 后端返回的组件路径，如 "@/views/user/RoleManagement.vue"
 * @returns {Function} 组件导入函数
 */
const getComponentImport = (componentPath) => {
  if (!componentPath) {
    console.warn('组件路径为空，使用默认组件')
    return getComponentImportFromConfig('@/views/Dashboard.vue')
  }

  // 从统一配置中获取组件导入函数
  const componentImport = getComponentImportFromConfig(componentPath)

  if (componentImport) {
    return componentImport
  } else {
    console.warn(`未找到组件映射: ${componentPath}，使用默认组件`)
    return getComponentImportFromConfig('@/views/Dashboard.vue')
  }
}

// 重新导出统一配置的函数
export { getAvailableComponents, getComponentDescription, getAllComponentsWithDescriptions } from '@/config/componentMap'

/**
 * 根据菜单数据生成路由配置
 * @param {Array} menus 菜单数据
 * @returns {Array} 路由配置数组
 */
export const generateRoutesFromMenus = (menus) => {
  const routes = []

  const processMenu = (menu, parentPath = '') => {
    // 构建当前菜单的完整路径
    let currentPath = parentPath

    // 如果是目录类型(1)，需要将其路径添加到父路径中
    if (menu.menuType === 1 && menu.routePath) {
      // 处理目录路径
      let dirPath = menu.routePath
      if (dirPath.startsWith('/')) {
        dirPath = dirPath.substring(1) // 去掉开头的 / 因为是子路由
      }

      // 构建新的父路径
      currentPath = parentPath ? `${parentPath}/${dirPath}` : dirPath
    }

    // 处理菜单类型(2)，生成实际的路由
    if (menu.menuType === 2 && menu.routePath) {
      // 处理内部链接菜单
      if (menu.externalLink !== 1 && menu.componentPath) {
        // 构建完整的路由路径
        let routePath = menu.routePath
        if (routePath.startsWith('/')) {
          routePath = routePath.substring(1) // 去掉开头的 / 因为是子路由
        }

        // 如果有父路径（来自目录），则组合路径
        const fullPath = currentPath ? `${currentPath}/${routePath}` : routePath

        const route = {
          path: fullPath, // 完整的层级路径
          name: menu.menuCode || fullPath.replace(/\//g, '_'),
          component: getComponentImport(menu.componentPath), // 使用后端返回的组件路径
          meta: {
            title: menu.menuName,
            breadcrumb: menu.menuName,
            permission: menu.permission,
            menuId: menu.id,
            icon: menu.icon,
            componentPath: menu.componentPath, // 保存原始组件路径用于调试
            parentPath: currentPath // 保存父路径信息
          }
        }

        // 如果有路由参数，解析并添加
        if (menu.routeParams) {
          try {
            route.meta.params = JSON.parse(menu.routeParams)
          } catch (error) {
            console.warn('解析路由参数失败:', menu.routeParams, error)
          }
        }

        routes.push(route)
        console.log(`生成路由: ${menu.menuName} -> ${fullPath} (原路径: ${menu.routePath})`)
      }
      // 处理外部链接菜单（当前窗口打开）
      else if (menu.externalLink === 1 && menu.openMode === '_self') {
        // 为外部链接生成特殊的路由，使用IframeView组件
        let routePath = 'link/' + menu.id

        // 如果有父路径，也需要加上
        const fullPath = currentPath ? `${currentPath}/${routePath}` : routePath

        const route = {
          path: fullPath,
          name: menu.menuCode || fullPath.replace(/\//g, '_'),
          component: () => import('@/views/IframeView.vue'), // 使用IframeView组件
          meta: {
            title: menu.menuName,
            breadcrumb: menu.menuName,
            permission: menu.permission,
            menuId: menu.id,
            icon: menu.icon,
            isExternal: true, // 标记为外部链接
            externalUrl: menu.originalExternalUrl || menu.routePath, // 使用原始外链地址
            parentPath: currentPath // 保存父路径信息
          }
        }

        routes.push(route)
        console.log(`生成外部链接路由: ${menu.menuName} -> ${fullPath} (${menu.routePath})`)
      } else if (menu.externalLink === 1 && menu.openMode === '_blank') {
        console.log(`跳过新窗口外部链接菜单: ${menu.menuName} -> ${menu.routePath}`)
      }
    }

    // 递归处理子菜单，传递当前路径
    if (menu.children && menu.children.length > 0) {
      menu.children.forEach(child => processMenu(child, currentPath))
    }
  }

  menus.forEach(menu => processMenu(menu))
  return routes
}

/**
 * 添加动态路由到路由器
 * @param {Object} router Vue Router实例
 * @param {Array} menus 菜单数据
 */
export const addDynamicRoutes = (router, menus) => {
  const dynamicRoutes = generateRoutesFromMenus(menus)

  if (dynamicRoutes.length === 0) {
    console.warn('没有生成任何动态路由')
    return
  }

  // 将动态路由添加为布局路由的子路由
  dynamicRoutes.forEach(route => {
    try {
      router.addRoute('Layout', route)
    } catch (error) {
      console.error(`添加路由失败: ${route.path}`, error)
    }
  })
}

/**
 * 移除动态路由
 * @param {Object} router Vue Router实例
 * @param {Array} routeNames 要移除的路由名称数组
 */
export const removeDynamicRoutes = (router, routeNames) => {
  routeNames.forEach(name => {
    if (router.hasRoute(name)) {
      router.removeRoute(name)
    }
  })
}

/**
 * 检查路由是否存在
 * @param {Object} router Vue Router实例
 * @param {string} path 路由路径
 * @returns {boolean}
 */
export const hasRoute = (router, path) => {
  const routes = router.getRoutes()
  return routes.some(route => route.path === path)
}

/**
 * 检查是否为动态路由路径
 * @param {string} path 路由路径
 * @param {Array} menus 菜单数据
 * @returns {boolean}
 */
export const isDynamicRoute = (path, menus) => {
  const checkMenu = (menu) => {
    if (menu.routePath === path && menu.menuType === 2) {
      return true
    }
    if (menu.children && menu.children.length > 0) {
      return menu.children.some(checkMenu)
    }
    return false
  }

  return menus.some(checkMenu)
}

/**
 * 路径匹配器
 * @param {string} pattern
 * @param {string} path
 * @returns {Boolean}
 */
export const isPathMatch = (pattern, path) => {
  const regexPattern = pattern.replace(/\//g, '\\/').replace(/\*\*/g, '.*').replace(/\*/g, '[^\\/]*')
  const regex = new RegExp(`^${regexPattern}$`)
  return regex.test(path)
}


/**
 * 获取所有动态路由
 * @param {Object} router Vue Router实例
 * @returns {Array} 动态路由列表
 */
export const getDynamicRoutes = (router) => {
  return router.getRoutes().filter(route =>
    route.meta && route.meta.menuId
  )
}
