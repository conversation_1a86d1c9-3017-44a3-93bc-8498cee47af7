/**
 * 事件监听器优化工具
 * 用于添加被动事件监听器，提高页面性能
 */

/**
 * 添加被动事件监听器
 * @param {Element} element - 目标元素
 * @param {string} event - 事件名称
 * @param {Function} handler - 事件处理函数
 * @param {boolean} passive - 是否为被动监听器
 */
export function addPassiveEventListener(element, event, handler, passive = true) {
  if (!element || !event || !handler) {
    console.warn('addPassiveEventListener: 缺少必要参数')
    return
  }

  const options = { passive }
  
  // 对于某些需要阻止默认行为的事件，不能设置为被动
  const nonPassiveEvents = ['touchmove', 'wheel', 'mousewheel']
  if (nonPassiveEvents.includes(event)) {
    options.passive = false
  }

  element.addEventListener(event, handler, options)
}

/**
 * 移除事件监听器
 * @param {Element} element - 目标元素
 * @param {string} event - 事件名称
 * @param {Function} handler - 事件处理函数
 */
export function removeEventListener(element, event, handler) {
  if (!element || !event || !handler) {
    console.warn('removeEventListener: 缺少必要参数')
    return
  }

  element.removeEventListener(event, handler)
}

/**
 * 批量添加被动事件监听器
 * @param {Element} element - 目标元素
 * @param {Array} events - 事件配置数组 [{ event, handler, passive }]
 */
export function addMultiplePassiveEventListeners(element, events) {
  if (!element || !Array.isArray(events)) {
    console.warn('addMultiplePassiveEventListeners: 参数错误')
    return
  }

  events.forEach(({ event, handler, passive = true }) => {
    addPassiveEventListener(element, event, handler, passive)
  })
}

/**
 * 批量移除事件监听器
 * @param {Element} element - 目标元素
 * @param {Array} events - 事件配置数组 [{ event, handler }]
 */
export function removeMultipleEventListeners(element, events) {
  if (!element || !Array.isArray(events)) {
    console.warn('removeMultipleEventListeners: 参数错误')
    return
  }

  events.forEach(({ event, handler }) => {
    removeEventListener(element, event, handler)
  })
}

/**
 * 检测是否支持被动事件监听器
 * @returns {boolean}
 */
export function supportsPassiveEvents() {
  let supportsPassive = false
  
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        supportsPassive = true
        return true
      }
    })
    window.addEventListener('testPassive', null, opts)
    window.removeEventListener('testPassive', null, opts)
  } catch (e) {
    // 不支持被动事件监听器
  }
  
  return supportsPassive
}

/**
 * 优化触摸事件监听器
 * 自动为触摸事件添加合适的被动选项
 */
export class TouchEventOptimizer {
  constructor(element) {
    this.element = element
    this.handlers = new Map()
  }

  /**
   * 添加触摸开始事件监听器
   * @param {Function} handler - 事件处理函数
   */
  addTouchStart(handler) {
    this.element.addEventListener('touchstart', handler, { passive: true })
    this.handlers.set('touchstart', handler)
  }

  /**
   * 添加触摸移动事件监听器
   * @param {Function} handler - 事件处理函数
   * @param {boolean} preventDefault - 是否需要阻止默认行为
   */
  addTouchMove(handler, preventDefault = false) {
    const options = { passive: !preventDefault }
    this.element.addEventListener('touchmove', handler, options)
    this.handlers.set('touchmove', handler)
  }

  /**
   * 添加触摸结束事件监听器
   * @param {Function} handler - 事件处理函数
   */
  addTouchEnd(handler) {
    this.element.addEventListener('touchend', handler, { passive: true })
    this.handlers.set('touchend', handler)
  }

  /**
   * 移除所有触摸事件监听器
   */
  removeAllTouchEvents() {
    this.handlers.forEach((handler, event) => {
      this.element.removeEventListener(event, handler)
    })
    this.handlers.clear()
  }

  /**
   * 销毁优化器
   */
  destroy() {
    this.removeAllTouchEvents()
    this.element = null
  }
}

/**
 * 创建触摸事件优化器
 * @param {Element} element - 目标元素
 * @returns {TouchEventOptimizer}
 */
export function createTouchEventOptimizer(element) {
  return new TouchEventOptimizer(element)
}
