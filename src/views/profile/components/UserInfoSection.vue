<template>
  <div class="user-info-section">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="4" animated />
    </div>

    <div v-else-if="userInfo" class="user-info-content">
      <!-- 用户头像和基本信息 -->
      <div class="user-basic-info">
        <div class="user-avatar">
          <UserAvatar
            :username="userInfo.username"
            :size="80"
          />
        </div>
        <div class="user-details">
          <h3 class="username">{{ userInfo.username }}</h3>
          <div class="user-tags">
            <el-tag
              type="primary"
              size="small"
            >
              {{ userInfo.userTypeName }}
            </el-tag>
            <el-tag
              :type="userInfo.accountLocked === 0 ? 'success' : 'danger'"
              size="small"
            >
              {{ userInfo.accountLocked === 0 ? '正常' : '已锁定' }}
            </el-tag>
          </div>
        </div>
        <div class="refresh-button">
          <el-button 
            type="primary" 
            size="small" 
            :loading="refreshing"
            @click="refreshUserInfo"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 账户信息 -->
      <div class="user-detailed-info">
        <el-descriptions title="账户信息" :column="descriptionsColumn" border>
          <el-descriptions-item label="用户类型">
            {{ userInfo.userTypeName }}
          </el-descriptions-item>
          <el-descriptions-item label="账户创建时间">
            {{ formatDateTime(userInfo.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录时间">
            {{ formatDateTime(userInfo.lastLoginTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录IP">
            {{ userInfo.lastLoginIp || '未知' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 安全信息 -->
      <div class="user-detailed-info">
        <el-descriptions title="安全信息" :column="descriptionsColumn" border>
          <el-descriptions-item label="谷歌验证器">
            <el-tag :type="userInfo.hasGoogleAuth ? 'success' : 'warning'" size="small">
              {{ userInfo.hasGoogleAuth ? '已设置' : '未设置' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="密码更新时间">
            {{ formatDateTime(userInfo.passwordUpdateTime) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-empty description="暂无用户信息" />
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import UserAvatar from '@/components/UserAvatar.vue'

// 使用auth store
const authStore = useAuthStore()
const refreshing = ref(false)
const isMobile = ref(false)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const loading = computed(() => authStore.loading && !userInfo.value)
const descriptionsColumn = computed(() => isMobile.value ? 1 : 2)

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    refreshing.value = true
    await authStore.fetchUserInfo(true) // 强制刷新
    ElMessage.success('用户信息刷新成功')
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('刷新用户信息失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.user-info-section {
  min-height: 200px;
}

.loading-container {
  padding: 20px 0;
}

.user-info-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 基本信息区域 */
.user-basic-info {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.username {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.user-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.refresh-button {
  flex-shrink: 0;
}

/* 详细信息区域 */
.user-detailed-info {
  margin-bottom: 24px;
}

.user-detailed-info:last-child {
  margin-bottom: 0;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .user-basic-info {
    flex-direction: column;
    text-align: center;
    gap: 16px;
    padding: 16px;
  }

  .user-details {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .username {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .user-basic-info {
    padding: 12px;
  }
  
  .username {
    font-size: 16px;
  }
  
  .user-detailed-info {
    padding: 12px;
  }
  
  .info-grid {
    gap: 8px;
  }
  
  .info-item {
    padding: 6px 0;
  }
}
</style>
