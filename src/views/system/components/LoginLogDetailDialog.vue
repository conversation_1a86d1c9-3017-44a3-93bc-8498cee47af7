<template>
  <el-dialog
    v-model="dialogVisible"
    title="登录日志详情"
    :width="dialogWidth"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    :fullscreen="isMobile"
    append-to-body
  >
    <div v-loading="loading" class="detail-content detail-dialog">
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="用户ID">
          {{ logData.userId || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ logData.username || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="登录时间">
          {{ formatDateTime(logData.loginTime) || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="登录状态">
          <el-tag :type="logData.loginStatus === 1 ? 'success' : 'danger'" size="small">
            {{ logData.loginStatus === 1 ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="失败原因" v-if="logData.loginStatus === 0">
          {{ logData.failureReason || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="会话ID">
          <span class="session-id">{{ logData.sessionId || '-' }}</span>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">网络信息</el-divider>
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="客户端IP">
          <span class="ip-address">{{ logData.clientIp || '-' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否代理">
          <el-tag :type="logData.isProxy === 1 ? 'warning' : 'success'" size="small">
            {{ logData.isProxy === 1 ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">地理位置</el-divider>
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="地理位置" span="2">
          {{ logData.location || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">设备信息</el-divider>
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="设备类型">
          <el-tag v-if="logData.deviceType" type="info" size="small">
            {{ logData.deviceType }}
          </el-tag>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="操作系统">
          {{ logData.osName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="浏览器">
          {{ logData.browserName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="User Agent" span="2">
          <div class="user-agent">
            {{ logData.userAgent || '-' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">安全信息</el-divider>
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="风险等级">
          <el-tag
            v-if="logData.riskLevel"
            :type="getRiskLevelType(logData.riskLevel)"
            size="small"
          >
            {{ getRiskLevelText(logData.riskLevel) }}
          </el-tag>
          <span v-else>正常</span>
        </el-descriptions-item>
        <el-descriptions-item label="可疑标记">
          <el-tag v-if="logData.isSuspicious === 1" type="warning" size="small">
            可疑
          </el-tag>
          <span v-else>正常</span>
        </el-descriptions-item>
        <el-descriptions-item label="可疑原因" span="2" v-if="logData.suspiciousReasons">
          <div class="suspicious-reasons">
            <el-tag
              v-for="reason in parseSuspiciousReasons(logData.suspiciousReasons)"
              :key="reason"
              type="warning"
              size="small"
              style="margin-right: 8px; margin-bottom: 4px;"
            >
              {{ reason }}
            </el-tag>
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">时间信息</el-divider>
      <el-descriptions :column="isMobile ? 1 : 2" border>
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(logData.createTime) || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDateTime(logData.updateTime) || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleViewUserLogs" v-if="logData.username">
          筛选该用户日志
        </el-button>
        <el-button type="info" @click="handleViewIpLogs" v-if="logData.clientIp">
          筛选该IP日志
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDateTime } from '@/utils/date'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  logData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'view-user-logs', 'view-ip-logs'])

// 响应式数据
const loading = ref(false)
const isMobile = ref(false)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 动态对话框宽度
const dialogWidth = computed(() => {
  if (isMobile.value) {
    return '100%'
  } else if (window.innerWidth <= 1024) {
    return '90%'
  } else {
    return '800px'
  }
})

// 方法
const handleClose = () => {
  dialogVisible.value = false
}

const handleViewUserLogs = () => {
  emit('view-user-logs', props.logData.username)
  handleClose()
}

const handleViewIpLogs = () => {
  emit('view-ip-logs', props.logData.clientIp)
  handleClose()
}

// 获取风险等级类型
const getRiskLevelType = (level) => {
  const typeMap = {
    'LOW': 'success',
    'MEDIUM': 'warning',
    'HIGH': 'danger'
  }
  return typeMap[level] || 'info'
}

// 获取风险等级文本
const getRiskLevelText = (level) => {
  const textMap = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高'
  }
  return textMap[level] || '未知'
}

// 解析可疑原因
const parseSuspiciousReasons = (reasons) => {
  if (!reasons) return []
  try {
    return Array.isArray(reasons) ? reasons : JSON.parse(reasons)
  } catch (error) {
    return typeof reasons === 'string' ? [reasons] : []
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.session-id,
.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: var(--text-color-primary);
}

.user-agent {
  word-break: break-all;
  line-height: 1.5;
  font-size: 13px;
  color: var(--text-color-regular);
  background: var(--bg-color-soft);
  padding: 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.suspicious-reasons {
  line-height: 1.8;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 页面特定样式 - 移动端适配已提取到公共样式文件 */
@media (max-width: 768px) {
  .user-agent {
    font-size: 12px;
    line-height: 1.4;
    word-break: break-all;
  }

  .suspicious-reasons {
    line-height: 1.6;
  }
}
</style>
