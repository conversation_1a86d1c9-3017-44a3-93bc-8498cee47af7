// 响应式断点混合
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (max-width: #{$breakpoint-sm - 1px}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (max-width: #{$breakpoint-xl - 1px}) {
      @content;
    }
  }
}

@mixin respond-above($breakpoint) {
  @if $breakpoint == xs {
    @media (min-width: #{$breakpoint-xs}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
}

// Flexbox 混合
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  @include flex-column;
  align-items: center;
  justify-content: center;
}

// 文字省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis-multiline($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin absolute-center-x {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

@mixin absolute-center-y {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

// 按钮样式混合
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: none;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  line-height: 1;
  cursor: pointer;
  transition: all $transition-normal;
  text-decoration: none;
  user-select: none;
  
  &:focus {
    outline: none;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--text-color-white);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-md);
  }

  &:active {
    transform: translateY(0);
  }
}

@mixin button-secondary {
  @include button-base;
  background: var(--bg-color);
  color: var(--text-color-primary);
  border: 1px solid var(--border-color-light);

  &:hover:not(:disabled) {
    background: var(--bg-color-hover);
    border-color: var(--border-color);
  }
}

// 卡片样式混合
@mixin card-base {
  background: var(--bg-color-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-md);
  border: 1px solid var(--border-color-light);
  overflow: hidden;
}

@mixin card-hover {
  transition: all var(--transition-normal);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
  }
}

// 输入框样式混合
@mixin input-base {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  transition: all var(--transition-normal);

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--box-shadow-focus);
  }

  &::placeholder {
    color: var(--text-color-placeholder);
  }
}

// 滚动条样式混合
@mixin scrollbar($width: 6px, $track-color: #f1f1f1, $thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $width / 2;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $width / 2;
    
    &:hover {
      background: darken($thumb-color, 10%);
    }
  }
}

// 毛玻璃效果
@mixin glass-effect($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: $backdrop-blur;
  border: 1px solid $glass-border;
}

// 渐变文字
@mixin gradient-text($gradient: $gradient-primary) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 动画混合
@mixin fade-in($duration: $transition-normal) {
  animation: fadeIn $duration ease-in-out;
}

@mixin slide-in-up($duration: $transition-normal) {
  animation: slideInUp $duration ease-out;
}

@mixin bounce-in($duration: 0.6s) {
  animation: bounceIn $duration ease-out;
}

// 关键帧动画
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 工具类混合
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

@mixin reset-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

@mixin reset-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  font: inherit;
  color: inherit;
}
