<template>
  <div class="route-params-editor">
    <div class="editor-header">
      <el-button 
        type="primary" 
        size="small" 
        @click="showVisualEditor = !showVisualEditor"
        :icon="showVisualEditor ? Edit : List"
      >
        {{ showVisualEditor ? '切换到JSON编辑' : '切换到可视化编辑' }}
      </el-button>
      <el-button 
        size="small" 
        @click="handleClear"
        :icon="Delete"
      >
        清空
      </el-button>
    </div>

    <!-- 可视化编辑器 -->
    <div v-if="showVisualEditor" class="visual-editor">
      <div class="params-list">
        <div
          v-for="(param, index) in paramsList"
          :key="index"
          class="param-item"
        >
          <el-row :gutter="12" align="middle">
            <el-col :span="7">
              <el-input
                v-model="param.key"
                placeholder="参数名"
                size="small"
                @input="updateJsonFromList"
              />
            </el-col>
            <el-col :span="8">
              <el-input
                v-model="param.value"
                placeholder="参数值"
                size="small"
                @input="updateJsonFromList"
              />
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="param.type"
                placeholder="类型"
                size="small"
                style="width: 100%"
                @change="updateJsonFromList"
              >
                <el-option label="字符串" value="string" />
                <el-option label="数字" value="number" />
                <el-option label="布尔值" value="boolean" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <div class="param-actions">
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click="removeParam(index)"
                  class="remove-btn"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <el-button
        type="dashed"
        size="default"
        style="width: 100%; margin-top: 12px; height: 40px;"
        @click="addParam"
        :icon="Plus"
      >
        添加参数
      </el-button>

      <div class="preview-section" v-if="paramsList.length > 0">
        <el-divider>预览</el-divider>
        <div class="json-preview">
          <code>{{ formatJsonPreview() }}</code>
        </div>
      </div>
    </div>

    <!-- JSON编辑器 -->
    <div v-else class="json-editor">
      <el-input
        v-model="jsonValue"
        type="textarea"
        :rows="6"
        placeholder="请输入JSON对象格式的路由参数，例如：&#10;{&#10;  &quot;id&quot;: &quot;123&quot;,&#10;  &quot;type&quot;: &quot;edit&quot;&#10;}"
        @input="updateListFromJson"
        style="width: 100%;"
      />
    </div>

    <!-- 错误提示 -->
    <div v-if="jsonError" class="error-message">
      <el-alert
        :title="jsonError"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Edit, List, Delete, Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const showVisualEditor = ref(true)
const jsonValue = ref(props.modelValue || '')
const jsonError = ref('')
const paramsList = ref([])

// 初始化参数列表
const initParamsList = () => {
  if (!jsonValue.value) {
    paramsList.value = []
    return
  }

  try {
    const parsed = JSON.parse(jsonValue.value)
    paramsList.value = Object.entries(parsed).map(([key, value]) => ({
      key,
      value: String(value),
      type: getValueType(value)
    }))
    jsonError.value = ''
  } catch (error) {
    jsonError.value = 'JSON格式错误：' + error.message
    paramsList.value = []
  }
}

// 获取值的类型
const getValueType = (value) => {
  if (typeof value === 'number') return 'number'
  if (typeof value === 'boolean') return 'boolean'
  return 'string'
}

// 转换值为正确的类型
const convertValue = (value, type) => {
  switch (type) {
    case 'number':
      const num = Number(value)
      return isNaN(num) ? 0 : num
    case 'boolean':
      return value === 'true' || value === true
    default:
      return String(value)
  }
}

// 从列表更新JSON
const updateJsonFromList = () => {
  try {
    const obj = {}
    const keys = []

    paramsList.value.forEach(param => {
      const key = param.key.trim()
      if (key) {
        // 收集键名用于重复检查
        keys.push(key)
        obj[key] = convertValue(param.value, param.type)
      }
    })

    // 检查重复的参数名
    const uniqueKeys = [...new Set(keys)]
    if (keys.length !== uniqueKeys.length) {
      jsonError.value = '参数名不能重复'
      return
    }

    jsonValue.value = Object.keys(obj).length > 0 ? JSON.stringify(obj, null, 2) : ''
    jsonError.value = ''
    emit('update:modelValue', jsonValue.value)
  } catch (error) {
    jsonError.value = '生成JSON失败：' + error.message
  }
}

// 从JSON更新列表
const updateListFromJson = () => {
  try {
    if (!jsonValue.value.trim()) {
      paramsList.value = []
      jsonError.value = ''
      emit('update:modelValue', '')
      return
    }

    const parsed = JSON.parse(jsonValue.value)

    // 路由参数必须是对象格式
    if (typeof parsed !== 'object' || parsed === null || Array.isArray(parsed)) {
      jsonError.value = '路由参数必须是对象格式，如：{"key": "value"}'
      paramsList.value = []
      return
    }

    paramsList.value = Object.entries(parsed).map(([key, value]) => ({
      key,
      value: String(value),
      type: getValueType(value)
    }))
    jsonError.value = ''
    emit('update:modelValue', jsonValue.value)
  } catch (error) {
    jsonError.value = 'JSON格式错误：' + error.message
    paramsList.value = []
  }
}

// 添加参数
const addParam = () => {
  paramsList.value.push({
    key: '',
    value: '',
    type: 'string'
  })
}

// 移除参数
const removeParam = (index) => {
  paramsList.value.splice(index, 1)
  updateJsonFromList()
}

// 清空所有参数
const handleClear = () => {
  paramsList.value = []
  jsonValue.value = ''
  jsonError.value = ''
  emit('update:modelValue', '')
}

// 格式化JSON预览
const formatJsonPreview = () => {
  try {
    const obj = {}
    paramsList.value.forEach(param => {
      if (param.key.trim()) {
        obj[param.key.trim()] = convertValue(param.value, param.type)
      }
    })
    return JSON.stringify(obj, null, 2)
  } catch (error) {
    return '格式错误'
  }
}

// 验证JSON格式
const validateJson = () => {
  debugger
  // 在可视化模式下，需要检查参数列表的有效性
  if (showVisualEditor.value) {

  }

  // 如果当前有错误信息，说明验证失败
  if (jsonError.value) {
    return false
  }

  if (!jsonValue.value.trim()) {
    jsonError.value = ''
    return true
  }

  try {
    const parsed = JSON.parse(jsonValue.value)

    // 路由参数必须是对象格式
    if (typeof parsed !== 'object' || parsed === null || Array.isArray(parsed)) {
      jsonError.value = '路由参数必须是对象格式，如：{"key": "value"}'
      return false
    }

    jsonError.value = ''
    return true
  } catch (error) {
    jsonError.value = 'JSON格式错误：' + error.message
    return false
  }
}

// 获取验证状态（供外部调用）
const isValid = () => {
  // 强制更新JSON以确保最新状态
  if (showVisualEditor.value) {
    updateJsonFromList()
    // 等待更新完成后再验证
    return !jsonError.value
  }
  return validateJson()
}

// 暴露验证方法给父组件
defineExpose({
  isValid,
  validateJson
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  jsonValue.value = newValue || ''
  initParamsList()
})

// 初始化
initParamsList()
</script>

<style lang="scss" scoped>
.route-params-editor {
  width: 100%;

  .editor-header {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
  }

  .visual-editor {
    .param-item {
      margin-bottom: 12px;
      padding: 16px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      background: var(--bg-color-card);
      transition: all 0.2s ease;
      box-shadow: var(--box-shadow-base);

      &:hover {
        border-color: var(--border-color-dark);
        background: var(--bg-color-page);
        box-shadow: var(--box-shadow-md);
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .param-actions {
      display: flex;
      justify-content: center;
      align-items: center;

      .remove-btn {
        width: 28px;
        height: 28px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--danger-color);
        background: transparent;
        border: 1px solid var(--danger-light);
        transition: all 0.2s ease;

        &:hover {
          background: var(--danger-lightest);
          border-color: var(--danger-color);
          color: var(--danger-dark);
          transform: scale(1.05);
        }

        &:active {
          transform: scale(0.95);
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }

    .preview-section {
      margin-top: 16px;

      .json-preview {
        padding: 12px;
        background: var(--bg-color-page);
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        color: var(--text-color-regular);
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }

  .json-editor {
    width: 100%;

    .el-textarea {
      width: 100%;
    }

    .json-tips {
      margin-top: 12px;

      .tips-content {
        p {
          margin: 4px 0;
          font-size: 13px;

          code {
            background: var(--bg-color-page);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          }
        }
      }
    }
  }

  .error-message {
    margin-top: 8px;
  }


}

:deep(.el-button--dashed) {
  border-style: dashed;
  color: var(--text-color-secondary);
  border-color: var(--border-color-dark);
  background: var(--bg-color-page);
  transition: all 0.2s ease;

  &:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: var(--primary-lightest);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>
