/**
 * 全局谷歌验证码管理器
 * 用于处理API接口的谷歌验证码二次验证
 */

import { createApp, ref } from 'vue'
import { ElMessage } from 'element-plus'
import ElementPlus from 'element-plus'
import GlobalGoogleAuthDialog from '@/components/GlobalGoogleAuthDialog.vue'

class GoogleAuthManager {
  constructor() {
    this.dialogInstance = null
    this.dialogApp = null
    this.pendingRequests = new Map() // 存储待重试的请求
    this.isDialogVisible = false
    this.currentRequestId = null
  }

  /**
   * 初始化谷歌验证码弹窗
   */
  init() {
    if (this.dialogInstance) return

    // 创建弹窗容器
    const container = document.createElement('div')
    container.id = 'google-auth-dialog-container'
    document.body.appendChild(container)

    // 创建Vue应用实例，传入事件处理器
    this.dialogApp = createApp(GlobalGoogleAuthDialog, {
      onSubmit: (authCode) => this.handleAuthSubmit(authCode),
      onCancel: () => this.handleAuthCancel()
    })

    // 注册Element Plus
    this.dialogApp.use(ElementPlus)

    // 挂载应用
    this.dialogInstance = this.dialogApp.mount(container)

    // 确保初始化时弹窗是隐藏的
    this.dialogInstance.hide()
  }



  /**
   * 显示谷歌验证码弹窗并处理请求重试
   * @param {Object} requestConfig 原始请求配置
   * @param {Function} retryFunction 重试函数
   * @returns {Promise} 返回重试结果的Promise
   */
  showAuthDialog(requestConfig, retryFunction) {
    return new Promise((resolve, reject) => {
      // 如果已经有弹窗显示，将请求加入队列
      const requestId = this.generateRequestId()
      
      this.pendingRequests.set(requestId, {
        config: requestConfig,
        retry: retryFunction,
        resolve,
        reject,
        timestamp: Date.now()
      })

      // 如果当前没有显示弹窗，显示弹窗
      if (!this.isDialogVisible) {
        this.currentRequestId = requestId
        this.showDialog()
      }
    })
  }

  /**
   * 显示弹窗
   */
  showDialog() {
    // 懒加载：只在需要时才初始化
    if (!this.dialogInstance) {
      this.init()
    }

    this.isDialogVisible = true
    this.dialogInstance.show()
  }

  /**
   * 隐藏弹窗
   */
  hideDialog() {
    if (this.dialogInstance) {
      this.dialogInstance.hide()
    }
    this.isDialogVisible = false
    this.currentRequestId = null
  }

  /**
   * 处理验证码提交
   * @param {string} authCode 谷歌验证码
   */
  async handleAuthSubmit(authCode) {
    if (!this.currentRequestId || !this.pendingRequests.has(this.currentRequestId)) {
      ElMessage.error('验证请求已失效，请重新操作')
      this.hideDialog()
      return
    }

    const requestInfo = this.pendingRequests.get(this.currentRequestId)
    this.dialogInstance.setLoading(true)

    try {
      // 在原始请求配置中添加谷歌验证码请求头
      const newConfig = {
        ...requestInfo.config,
        headers: {
          ...requestInfo.config.headers,
          'X-Google-Auth-Code': authCode
        }
      }

      // 重试请求
      const result = await requestInfo.retry(newConfig)
      
      // 请求成功，移除待处理请求并关闭弹窗
      this.pendingRequests.delete(this.currentRequestId)
      this.hideDialog()
      
      // 处理队列中的下一个请求
      this.processNextRequest()
      
      // 解析Promise
      requestInfo.resolve(result)
      
    } catch (error) {
      this.dialogInstance.setLoading(false)
      
      // 检查是否是谷歌验证码错误
      if (error.code === -9998) {
        this.dialogInstance.setError('谷歌验证码错误，请重新输入')
      } else if (error.code === -9999) {
        this.dialogInstance.setError('验证码已过期，请重新输入')
      } else {
        // 其他错误，关闭弹窗并拒绝Promise
        this.pendingRequests.delete(this.currentRequestId)
        this.hideDialog()
        this.processNextRequest()
        requestInfo.reject(error)
      }
    }
  }

  /**
   * 处理验证取消
   */
  handleAuthCancel() {
    if (this.currentRequestId && this.pendingRequests.has(this.currentRequestId)) {
      const requestInfo = this.pendingRequests.get(this.currentRequestId)
      this.pendingRequests.delete(this.currentRequestId)
      
      // 创建取消错误
      const cancelError = new Error('用户取消谷歌验证码验证')
      cancelError.code = 'GOOGLE_AUTH_CANCELLED'
      requestInfo.reject(cancelError)
    }

    this.hideDialog()
    this.processNextRequest()
  }

  /**
   * 处理队列中的下一个请求
   */
  processNextRequest() {
    if (this.pendingRequests.size > 0) {
      // 获取最早的请求
      const oldestRequest = Array.from(this.pendingRequests.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)[0]
      
      if (oldestRequest) {
        this.currentRequestId = oldestRequest[0]
        this.showDialog()
      }
    }
  }

  /**
   * 生成请求ID
   * @returns {string}
   */
  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 清理过期的请求
   * @param {number} maxAge 最大存活时间（毫秒）
   */
  cleanupExpiredRequests(maxAge = 5 * 60 * 1000) { // 默认5分钟
    const now = Date.now()
    for (const [requestId, requestInfo] of this.pendingRequests.entries()) {
      if (now - requestInfo.timestamp > maxAge) {
        const timeoutError = new Error('谷歌验证码验证超时')
        timeoutError.code = 'GOOGLE_AUTH_TIMEOUT'
        requestInfo.reject(timeoutError)
        this.pendingRequests.delete(requestId)
      }
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    // 拒绝所有待处理的请求
    for (const [requestId, requestInfo] of this.pendingRequests.entries()) {
      const destroyError = new Error('谷歌验证码管理器已销毁')
      destroyError.code = 'GOOGLE_AUTH_DESTROYED'
      requestInfo.reject(destroyError)
    }
    
    this.pendingRequests.clear()
    
    // 销毁Vue应用实例
    if (this.dialogApp) {
      this.dialogApp.unmount()
      this.dialogApp = null
    }
    
    // 移除DOM容器
    const container = document.getElementById('google-auth-dialog-container')
    if (container) {
      document.body.removeChild(container)
    }
    
    this.dialogInstance = null
    this.isDialogVisible = false
    this.currentRequestId = null
  }
}

// 创建全局单例
const googleAuthManager = new GoogleAuthManager()

// 定期清理过期请求
setInterval(() => {
  googleAuthManager.cleanupExpiredRequests()
}, 60 * 1000) // 每分钟清理一次

export default googleAuthManager
