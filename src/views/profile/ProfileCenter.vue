<template>
  <div class="profile-center">
    <div class="profile-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">个人中心</h1>
        <p class="page-description">管理您的个人信息、安全设置和账户配置</p>
      </div>

      <!-- 功能模块 -->
      <div class="profile-modules">
        <!-- 用户信息模块 -->
        <div class="module-card">
          <div class="module-header">
            <div class="module-title">
              <el-icon class="module-icon"><User /></el-icon>
              <span>基本信息</span>
            </div>
          </div>
          <div class="module-content">
            <UserInfoSection />
          </div>
        </div>

        <!-- 密码管理模块 -->
        <div class="module-card">
          <div class="module-header">
            <div class="module-title">
              <el-icon class="module-icon"><Lock /></el-icon>
              <span>密码管理</span>
            </div>
          </div>
          <div class="module-content">
            <PasswordManagement />
          </div>
        </div>

        <!-- 谷歌验证管理模块 -->
        <div class="module-card">
          <div class="module-header">
            <div class="module-title">
              <el-icon class="module-icon"><Key /></el-icon>
              <span>谷歌验证器</span>
            </div>
          </div>
          <div class="module-content">
            <GoogleAuthManagement />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { User, Lock, Key } from '@element-plus/icons-vue'
import UserInfoSection from './components/UserInfoSection.vue'
import PasswordManagement from './components/PasswordManagement.vue'
import GoogleAuthManagement from './components/GoogleAuthManagement.vue'

// 设置页面标题
document.title = '个人中心 - aKey'
</script>

<style scoped>
.profile-center {
  min-height: 100vh;
  background: var(--el-bg-color-page);
  padding: 20px;
}

.profile-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

/* 功能模块 */
.profile-modules {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.module-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.module-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.module-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color-page);
}

.module-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.module-icon {
  margin-right: 8px;
  font-size: 18px;
  color: var(--el-color-primary);
}

.module-content {
  padding: 24px;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .profile-modules {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }
}

@media (min-width: 1200px) {
  .profile-modules {
    grid-template-columns: 1fr 1fr;
  }
  
  .module-card:first-child {
    grid-column: 1 / -1;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .profile-center {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .profile-modules {
    gap: 16px;
  }
  
  .module-header {
    padding: 16px 20px;
  }
  
  .module-content {
    padding: 20px;
  }
  
  .module-title {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .profile-center {
    padding: 12px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .page-description {
    font-size: 13px;
  }
  
  .module-header {
    padding: 12px 16px;
  }
  
  .module-content {
    padding: 16px;
  }
  
  .module-title {
    font-size: 14px;
  }
  
  .module-icon {
    font-size: 16px;
  }
}
</style>
