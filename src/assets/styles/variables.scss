// SCSS变量 - 与CSS变量保持一致
// 主色调 - 蓝色系高端配色
$primary-color: #3B82F6;
$primary-light: #60A5FA;
$primary-lighter: #93C5FD;
$primary-lightest: #DBEAFE;
$primary-dark: #2563EB;
$primary-darker: #1D4ED8;

// 功能色
$success-color: #10B981;
$success-light: #34D399;
$success-dark: #059669;
$warning-color: #F59E0B;
$warning-light: #FBBF24;
$warning-dark: #D97706;
$danger-color: #EF4444;
$danger-light: #F87171;
$danger-dark: #DC2626;
$info-color: #6B7280;
$info-light: #9CA3AF;
$info-dark: #4B5563;

// 中性色
$white: #ffffff;
$black: #000000;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// 背景色 - 现代简洁
$bg-color: #FFFFFF;
$bg-color-page: #F8FAFC;
$bg-color-overlay: #FFFFFF;
$bg-color-sidebar: #1E293B;
$bg-color-header: #FFFFFF;
$bg-color-card: #FFFFFF;
$bg-color-hover: #F1F5F9;

// 边框色 - 精致细腻
$border-color: #E5E7EB;
$border-color-light: #F3F4F6;
$border-color-lighter: #F9FAFB;
$border-color-extra-light: #FAFBFC;
$border-color-dark: #D1D5DB;
$border-color-darker: #9CA3AF;

// 文字色 - 层次分明
$text-color-primary: #1F2937;
$text-color-regular: #374151;
$text-color-secondary: #6B7280;
$text-color-placeholder: #9CA3AF;
$text-color-disabled: #D1D5DB;
$text-color-white: #FFFFFF;
$text-color-light: #F3F4F6;
$text-color-inverse: #FFFFFF;

// 尺寸变量
// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;

// 圆角
$radius-sm: 4px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;
$radius-full: 50%;

// 阴影
$shadow-sm: 0 1px 2px rgba(59, 130, 246, 0.05);
$shadow-md: 0 4px 12px rgba(59, 130, 246, 0.08);
$shadow-lg: 0 8px 24px rgba(59, 130, 246, 0.12);
$shadow-xl: 0 16px 32px rgba(59, 130, 246, 0.16);

// 字体
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 28px;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.2;
$line-height-normal: 1.5;
$line-height-relaxed: 1.6;
$line-height-loose: 2;

// 布局变量
// 头部高度
$header-height: 60px;

// 侧边栏宽度
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;

// 容器最大宽度
$container-max-width: 1200px;

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1280px;
$breakpoint-xl: 1536px;

// Z-index层级
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 过渡动画
$transition-fast: 0.15s ease;
$transition-normal: 0.3s ease;
$transition-slow: 0.5s ease;

// 渐变
$gradient-primary: linear-gradient(135deg, #{$primary-color} 0%, #{$primary-dark} 100%);
$gradient-success: linear-gradient(135deg, #{$success-color} 0%, #059669 100%);
$gradient-warning: linear-gradient(135deg, #{$warning-color} 0%, #d97706 100%);
$gradient-danger: linear-gradient(135deg, #{$danger-color} 0%, #dc2626 100%);

// 特殊效果
$backdrop-blur: blur(8px);
$glass-bg: rgba(255, 255, 255, 0.1);
$glass-border: rgba(255, 255, 255, 0.2);
