<template>
  <div class="login-container">
    <!-- 主题切换按钮 -->
    <div class="theme-toggle-container">
      <theme-toggle size="medium" />
    </div>

    <div class="login-wrapper">
      <!-- 左侧区域 -->
      <div class="login-left">
        <div class="left-content">
          <h1 class="welcome-title">Hi! 你好!</h1>
          <div class="illustration-container">
            <img 
              src="../assets/images/login_img_bg.svg" 
              alt="数据管理插画" 
              class="illustration-image"
            />
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="login-right">
        <div class="right-content">
          <h2 class="login-title">{{ currentStep === 'login' ? 'Welcome' : '二次验证' }}</h2>

          <!-- 第一步：用户名密码登录 -->
          <el-form
            v-show="currentStep === 'login'"
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            :class="{ 'shake-animation': showShakeAnimation }"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username" class="form-item">
              <el-input
                v-model="loginForm.username"
                type="username"
                placeholder="请输入用户名"
                size="large"
                :prefix-icon="User"
                clearable
              />
            </el-form-item>

            <el-form-item prop="password" class="form-item">
              <el-input
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
                size="large"
                :prefix-icon="Lock"
                clearable
              >
                <template #suffix>
                  <el-icon 
                    class="password-toggle" 
                    @click="togglePassword"
                  >
                    <component :is="showPassword ? Hide : View" />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="captcha" class="form-item">
              <div class="captcha-container">
                <div class="captcha-image-wrapper">
                  <img
                    :src="captchaImageSrc"
                    alt="验证码"
                    class="captcha-image"
                    @click="refreshCaptcha"
                  />
                  <div
                    v-show="captchaLoading"
                    class="captcha-loading-overlay"
                  >
                    <el-icon class="loading-icon">
                      <Loading />
                    </el-icon>
                  </div>
                </div>
                <el-input
                  v-model="loginForm.captcha"
                  placeholder="请输入验证码"
                  size="large"
                  class="captcha-input"
                  maxlength="4"
                  clearable
                />
              </div>
            </el-form-item>

            <div class="form-options">
              <el-checkbox v-model="rememberMe" class="remember-checkbox">
                记住我
              </el-checkbox>
            </div>

            <el-form-item class="form-item">
              <el-button
                type="primary"
                size="large"
                class="login-button"
                :loading="authStore.loading"
                @click="handleLogin"
              >
                立即登录
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 第二步：谷歌验证码输入 -->
          <div v-show="currentStep === 'google-auth'" class="google-auth-form">
            <div class="google-auth-header">
              <div class="auth-icon">
                <el-icon size="48" color="#4285f4">
                  <Lock />
                </el-icon>
              </div>
              <h3 class="auth-title">谷歌验证器</h3>
              <p class="auth-description">
                请打开谷歌验证器应用，输入显示的6位验证码
              </p>
            </div>

            <div class="google-auth-input">
              <VerificationCodeInput
                ref="googleAuthCodeRef"
                v-model="googleAuthCode"
                :auto-submit="true"
                :disabled="authStore.loading"
                @complete="handleGoogleAuthComplete"
                @submit="handleGoogleAuthSubmit"
              />
            </div>

            <div class="google-auth-actions">
              <el-button
                size="large"
                class="back-button"
                :disabled="authStore.loading"
                @click="backToLogin"
              >
                返回登录
              </el-button>
              <el-button
                type="primary"
                size="large"
                class="verify-button"
                :loading="authStore.loading"
                :disabled="googleAuthCode.length !== 6"
                @click="handleGoogleAuthSubmit"
              >
                验证登录
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="copyright-info">
      <p>&copy; 2025 aKey. All rights reserved.</p>
      <p>Powered by aKey</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, View, Hide, Loading } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useLogStore } from '@/stores/log'
import VerificationCodeInput from '@/components/VerificationCodeInput.vue'

const router = useRouter()
const authStore = useAuthStore()
const logStore = useLogStore()

// 响应式数据
const loginFormRef = ref()
const googleAuthCodeRef = ref()
const showPassword = ref(false)
const rememberMe = ref(false)
const showShakeAnimation = ref(false)
const captchaLoading = ref(false)
const currentStep = ref('login') // 'login' | 'google-auth'
const googleAuthCode = ref('')
const savedLoginData = ref(null) // 保存第一步的登录数据

// 计算属性
const captchaImageSrc = computed(() => {
  return authStore.captchaData.imageBase64
    ? authStore.captchaData.imageBase64
    : '../assets/images/code.png'
})

// 表单数据
const loginForm = reactive({
  username: 'admin',
  password: '123456',
  captcha: '1234'
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 1, max: 10, message: '请输入验证码', trigger: 'blur' }
  ]
}

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 触发震动动画
const triggerShakeAnimation = () => {
  showShakeAnimation.value = true
  setTimeout(() => {
    showShakeAnimation.value = false
  }, 600)
}

// 刷新验证码
const refreshCaptcha = async () => {
  try {
    captchaLoading.value = true
    // 获取新的验证码
    await authStore.getCaptcha()
    // 刷新成功后清空验证码输入
    loginForm.captcha = ''
    ElMessage.success('验证码已刷新')
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('刷新验证码失败:', error)
  } finally {
    captchaLoading.value = false
  }
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 验证表单
    await loginFormRef.value.validate()

    // 保存登录数据，用于后续的谷歌验证
    savedLoginData.value = {
      username: loginForm.username,
      password: loginForm.password,
      captcha: loginForm.captcha
    }

    // 调用登录接口
    const response = await authStore.login(savedLoginData.value)

    // 检查业务状态码是否需要谷歌验证码
    if (response && response.code === 9999) {
      // 需要谷歌验证码，切换到谷歌验证界面
      currentStep.value = 'google-auth'
      googleAuthCode.value = ''
      // 自动聚焦到谷歌验证码输入框
      setTimeout(() => {
        if (googleAuthCodeRef.value) {
          googleAuthCodeRef.value.focus()
        }
      }, 100)
      return
    }

    // 登录成功，处理记住我功能和跳转
    await handleLoginSuccess()
  } catch (error) {
    console.error('登录错误:', error)

    if (error.message && error.message.includes('validate')) {
      // 表单验证失败
      triggerShakeAnimation()
    } else {
      // 网络或其他错误
      triggerShakeAnimation()
      // 错误时清空验证码
      loginForm.captcha = ''
    }
  }
}

// 处理登录成功后的逻辑
const handleLoginSuccess = async () => {
  // 如果勾选了"记住我"，保存加密的登录信息
  if (rememberMe.value) {
    try {
      logStore.saveLoginInfo(loginForm.username, loginForm.password)
    } catch (error) {
      console.error('保存登录信息失败:', error)
      // 保存失败不影响登录流程，只记录错误
    }
  } else {
    // 如果没有勾选"记住我"，清除之前保存的信息
    logStore.clearLoginInfo()
  }

  // 获取重定向路径并跳转
  const redirectPath = authStore.getRedirectPath()
  router.push(redirectPath)
}

// 谷歌验证码输入完成
const handleGoogleAuthComplete = (code) => {
  console.log('谷歌验证码输入完成:', code)
}

// 提交谷歌验证码
const handleGoogleAuthSubmit = async () => {
  if (googleAuthCode.value.length !== 6) {
    ElMessage.warning('请输入完整的6位验证码')
    return
  }

  if (!savedLoginData.value) {
    ElMessage.error('登录数据丢失，请重新登录')
    backToLogin()
    return
  }

  try {
    // 调用登录接口，包含谷歌验证码
    const response = await authStore.login({
      ...savedLoginData.value,
      googleAuthCode: googleAuthCode.value
    })

    // 检查谷歌验证码是否错误
    if (response && response.code === -9998) {
      ElMessage.error('谷歌验证码错误，请重新输入')
      // 清空验证码输入框
      googleAuthCode.value = ''
      if (googleAuthCodeRef.value) {
        googleAuthCodeRef.value.clear()
      }
      return
    }

    // 检查是否还需要谷歌验证码（不应该出现，但防御性编程）
    if (response && response.code === 9999) {
      ElMessage.error('验证状态异常，请重新登录')
      backToLogin()
      return
    }

    // 如果 auth store 没有抛出异常且没有返回错误码，说明登录成功
    // 登录成功，处理记住我功能和跳转
    await handleLoginSuccess()
  } catch (error) {
    // 清空验证码输入框
    googleAuthCode.value = ''
    if (googleAuthCodeRef.value) {
      googleAuthCodeRef.value.clear()
    }
  }
}

// 返回登录界面
const backToLogin = () => {
  currentStep.value = 'login'
  googleAuthCode.value = ''
  savedLoginData.value = null
  // 清空登录表单的验证码
  loginForm.captcha = ''
}

// 页面加载时获取验证码和自动填充登录信息
onMounted(async () => {
  try {
    captchaLoading.value = true
    await authStore.getCaptcha()
  } catch (error) {
    // 验证码加载失败是关键功能，保留错误提示
    console.error('初始化验证码失败:', error)
    ElMessage.error('验证码加载失败，请刷新页面重试')
  } finally {
    captchaLoading.value = false
  }

  // 检查是否有保存的登录信息
  try {
    if (logStore.hasLoginInfo()) {
      const savedLoginInfo = logStore.getLoginInfo()
      if (savedLoginInfo) {
        loginForm.username = savedLoginInfo.username
        loginForm.password = savedLoginInfo.password
        rememberMe.value = true

      }
    }
  } catch (error) {
    console.error('加载保存的登录信息失败:', error)
    // 如果加载失败，清除可能损坏的数据
    logStore.clearLoginInfo()
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-color-page) 0%, var(--border-color-light) 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: hidden;

  // 背景装饰元素
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
      radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(59, 130, 246, 0.08) 0%, transparent 50%);
    animation: float-bg 25s ease-in-out infinite;
    z-index: 0;
  }

  // 动态背景粒子
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(1px 1px at 20px 30px, rgba(255, 255, 255, 0.4), transparent),
      radial-gradient(1px 1px at 40px 70px, rgba(255, 255, 255, 0.3), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.5), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.3), transparent),
      radial-gradient(1px 1px at 160px 30px, rgba(255, 255, 255, 0.4), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
    z-index: 0;
  }
}

// 主题切换按钮容器
.theme-toggle-container {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.login-wrapper {
  width: 100%;
  max-width: 1000px;
  height: 600px;
  background: var(--bg-color-overlay);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid var(--border-color-light);
  border-radius: 24px;
  box-shadow: var(--box-shadow-xl);
  display: flex;
  overflow: hidden;
  position: relative;
  z-index: 1;

  // 内部光晕效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    z-index: 1;
  }
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;

  // 左侧装饰效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 60%),
      radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 60%);
    pointer-events: none;
  }
}

.left-content {
  text-align: center;
  color: var(--text-color-white);
}

.welcome-title {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 32px;
  line-height: 1.4;
}

.illustration-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.illustration-image {
  width: auto;
  height: 300px;
  max-width: 100%;
}

.login-right {
  flex: 1;
  background: var(--bg-color-card);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;

  // 右侧边框效果
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10%;
    bottom: 10%;
    width: 1px;
    background: linear-gradient(to bottom, transparent, var(--primary-color), transparent);
  }
}

.right-content {
  width: 100%;
  max-width: 400px;
}

.login-title {
  font-size: 22px;
  font-weight: bold;
  color: var(--text-color-primary);
  text-align: center;
  margin-bottom: 32px;
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.remember-checkbox {
  color: var(--text-color-secondary);
}

.password-toggle {
  cursor: pointer;
  color: var(--text-color-placeholder);

  &:hover {
    color: var(--primary-color);
  }
}

// 验证码容器样式
.captcha-container {
  display: flex;
  gap: 12px;
  align-items: stretch; // 改为stretch确保高度一致
  width: 100%; // 确保容器占满宽度
}

.captcha-image-wrapper {
  position: relative;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  width: 120px;
  height: 40px;
}

.captcha-image {
  border-radius: 10px; // 与输入框圆角一致
  border: 1px solid var(--border-color-light);
  background: var(--bg-color-card);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  width: 120px;
  height: 40px;
  cursor: pointer;
  transition: all 0.3s ease;
  object-fit: cover;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px var(--box-shadow-focus);
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }
}

.captcha-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 10px;
  background: var(--bg-color-overlay);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  .loading-icon {
    font-size: 18px;
    color: var(--primary-color);
    animation: rotate 1s linear infinite;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  &:hover {
    background: var(--bg-color-hover);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.captcha-input {
  flex: 1;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 18px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid var(--border-color-light) !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  letter-spacing: 0.5px;

  &:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color)) !important;
    transform: translateY(-3px);
    box-shadow:
      0 12px 30px var(--box-shadow-focus),
      0 0 25px rgba(255, 255, 255, 0.3) inset;
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
  }

  // 按钮内部光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
  }

  &:hover::before {
    left: 100%;
  }

  // 按钮点击波纹效果
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: var(--bg-color-overlay);
    transform: translate(-50%, -50%);
    transition: width 0.4s ease-out, height 0.4s ease-out, opacity 0.4s ease-out;
    opacity: 0;
    z-index: 1;
  }

  &:active::after {
    width: 400px;
    height: 400px;
    opacity: 1;
  }

  // 加载状态特效
  &.is-loading {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark), var(--primary-color)) !important;
    background-size: 200% 200%;
    animation: gradient-shift 1.5s ease infinite;
  }
}

// 震动动画
.shake-animation {
  animation: shake 0.6s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }
  20%, 40%, 60%, 80% { transform: translateX(8px); }
}

// 按钮渐变动画
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

// 背景浮动动画
@keyframes float-bg {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

// 背景粒子闪烁动画
@keyframes sparkle {
  0%, 100% { opacity: 1; transform: translateY(0); }
  50% { opacity: 0.3; transform: translateY(-10px); }
}

// 表单项动画
.form-item {
  transition: all 0.3s ease;

  &:focus-within {
    transform: translateY(-2px);
  }
}

// 输入框默认样式
:deep(.el-input__wrapper) {
  background: var(--bg-color-card) !important;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--border-color-light) !important;
  border-radius: 10px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
// 输入框聚焦效果 - 只在非错误状态下应用自定义样式
:deep(.el-form-item:not(.is-error) .el-input__wrapper) {
  &:hover {
    background: var(--bg-color-hover) !important;
    border-color: var(--primary-color) !important;
    box-shadow: var(--box-shadow-base);
  }
  &.is-focus {
    background: var(--bg-color-card) !important;
    border-color: var(--primary-color) !important;
    box-shadow: var(--box-shadow-focus), var(--box-shadow-base);
  }
}


// 复选框动画
:deep(.el-checkbox) {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

// 页面进入动画
.login-wrapper {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 左侧内容动画
.left-content {
  animation: slideInLeft 1s ease-out 0.2s both;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 右侧内容动画
.right-content {
  animation: slideInRight 1s ease-out 0.4s both;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 插图动画
.illustration-image {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

// 版权信息样式
.copyright-info {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: var(--primary-color);
  font-size: 12px;
  z-index: 2;

  p {
    margin: 2px 0;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
  }

  p:first-child {
    font-weight: 500;
    color: var(--primary-color);
  }

  p:last-child {
    opacity: 0.6;
    font-size: 11px;
    color: var(--primary-dark);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 10px 10px 80px 10px; // 底部增加空间避免重叠
  }

  .login-wrapper {
    flex-direction: column;
    height: auto;
    max-width: 400px;
    margin-bottom: 20px; // 与版权信息保持距离
  }

  .login-left {
    padding: 30px 20px;
  }

  .welcome-title {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .illustration-image {
    height: 200px;
  }

  .login-right {
    padding: 30px 20px 40px 20px; // 底部增加padding
  }

  // 版权信息在移动端调整位置
  .copyright-info {
    position: fixed; // 改为fixed定位
    bottom: 10px;
    font-size: 11px;

    p {
      margin: 1px 0;
    }
  }

  // 移动端减少动画
  .login-wrapper,
  .left-content,
  .right-content {
    animation: none;
  }

  .illustration-image {
    animation: none;
  }

  // 移动端主题切换按钮位置调整
  .theme-toggle-container {
    top: 15px;
    right: 15px;
    z-index: 20; // 确保在最上层
  }
}

// 小屏幕手机适配
@media (max-width: 480px) {
  .login-container {
    padding: 5px 5px 60px 5px;
  }

  .theme-toggle-container {
    top: 10px;
    right: 10px;
    z-index: 20;
  }

  .login-wrapper {
    max-width: 350px;
    border-radius: 16px;
  }

  .login-left {
    padding: 20px 15px;
  }

  .login-right {
    padding: 20px 15px 30px 15px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .illustration-image {
    height: 150px;
  }

  // 移动端谷歌验证码界面适配
  .google-auth-header {
    .auth-title {
      font-size: 20px;
    }

    .auth-description {
      font-size: 13px;
    }
  }

  .google-auth-actions {
    flex-direction: column;
    gap: 12px;

    .back-button,
    .verify-button {
      height: 44px;
      font-size: 15px;
    }
  }

  // 确保版权信息在小屏幕下也被隐藏
  .copyright-info {
    display: none;
  }
}

// 谷歌验证码界面样式
.google-auth-form {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.google-auth-header {
  text-align: center;
  margin-bottom: 32px;

  .auth-icon {
    margin-bottom: 16px;
  }

  .auth-title {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color-primary);
  }

  .auth-description {
    margin: 0;
    font-size: 14px;
    color: var(--text-color-regular);
    line-height: 1.5;
  }
}

.google-auth-input {
  margin: 32px 0;
  display: flex;
  justify-content: center;
}

.google-auth-actions {
  display: flex;
  gap: 16px;
  margin-top: 32px;

  .back-button,
  .verify-button {
    flex: 1;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .back-button {
    background: var(--bg-color-page);
    border: 1px solid var(--border-color);
    color: var(--text-color-regular);

    &:hover {
      background: var(--bg-color-overlay);
      border-color: var(--border-color-hover);
      transform: translateY(-2px);
    }
  }

  .verify-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
    border: 1px solid var(--border-color-light) !important;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, var(--primary-dark), var(--primary-color)) !important;
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(var(--primary-color-rgb), 0.4);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    // 按钮内部光效
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      transition: left 0.6s;
    }

    &:hover:not(:disabled)::before {
      left: 100%;
    }
  }
}
</style>
