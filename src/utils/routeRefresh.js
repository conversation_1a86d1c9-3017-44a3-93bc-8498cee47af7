/**
 * 路由刷新工具
 * 用于在菜单变更后刷新用户路由
 */

import { useMenuStore } from '@/stores/menu'
import { addDynamicRoutes } from '@/utils/dynamicRoutes'

/**
 * 刷新用户路由
 * @param {Object} router Vue Router实例
 * @returns {Promise<void>}
 */
export const refreshUserRoutes = async (router) => {
  try {
    console.log('开始刷新用户路由...')
    
    const menuStore = useMenuStore()
    
    // 清除当前菜单数据和路由状态
    menuStore.clearMenus()
    
    // 重新获取用户菜单
    await menuStore.fetchMenus()
    
    // 移除所有动态路由
    const routes = router.getRoutes()
    routes.forEach(route => {
      if (route.meta && route.meta.menuId) {
        try {
          router.removeRoute(route.name)
        } catch (error) {
          console.warn(`移除路由失败: ${route.name}`, error)
        }
      }
    })
    
    // 重新添加动态路由
    addDynamicRoutes(router, menuStore.menus)
    menuStore.markRoutesAdded()
    
    console.log('用户路由刷新完成')
  } catch (error) {
    console.error('刷新用户路由失败:', error)
    throw error
  }
}

/**
 * 创建路由刷新事件
 * 用于跨组件通信
 */
class RouteRefreshEvent {
  constructor() {
    this.listeners = []
  }
  
  /**
   * 添加监听器
   * @param {Function} callback 回调函数
   */
  addListener(callback) {
    this.listeners.push(callback)
  }
  
  /**
   * 移除监听器
   * @param {Function} callback 回调函数
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  /**
   * 触发路由刷新事件
   */
  emit() {
    this.listeners.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.error('路由刷新监听器执行失败:', error)
      }
    })
  }
}

// 创建全局路由刷新事件实例
export const routeRefreshEvent = new RouteRefreshEvent()

/**
 * 触发路由刷新
 * 在菜单变更后调用此方法
 */
export const triggerRouteRefresh = () => {
  console.log('触发路由刷新事件')
  routeRefreshEvent.emit()
}
