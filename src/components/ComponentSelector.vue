<template>
  <div class="component-selector">
    <el-input
      v-model="selectedComponent"
      placeholder="请选择或输入组件路径"
      readonly
      @click="dialogVisible = true"
      style="width: 100%"
    >
      <template #prefix>
        <el-icon class="component-icon">
          <Document />
        </el-icon>
      </template>
      <template #suffix>
        <el-icon class="cursor-pointer" @click="dialogVisible = true">
          <ArrowDown />
        </el-icon>
      </template>
    </el-input>

    <el-dialog
      v-model="dialogVisible"
      title="选择组件"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="component-selector-content">
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索组件..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="component-list">
          <div
            v-for="componentPath in filteredComponents"
            :key="componentPath"
            class="component-item"
            :class="{ active: selectedComponent === componentPath }"
            @click="handleSelectComponent(componentPath)"
          >
            <div class="component-info">
              <div class="component-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="component-details">
                <div class="component-path">{{ componentPath }}</div>
                <div class="component-description">{{ getComponentDescriptionLocal(componentPath) }}</div>
              </div>
            </div>
            <div class="component-actions" v-if="selectedComponent === componentPath">
              <el-icon class="check-icon"><Check /></el-icon>
            </div>
          </div>
        </div>

        <div v-if="filteredComponents.length === 0" class="empty-state">
          <el-empty description="未找到匹配的组件" />
        </div>

        <div class="custom-input-section">
          <el-divider>或者手动输入</el-divider>
          <el-input
            v-model="customInput"
            placeholder="请输入自定义组件路径，如：@/views/custom/MyComponent.vue"
          />
        </div>
      </div>

      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleClear">清空</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ArrowDown, Search, Document, Check } from '@element-plus/icons-vue'
import { getAvailableComponents, getComponentDescription as getComponentDesc } from '@/config/componentMap'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const dialogVisible = ref(false)
const searchKeyword = ref('')
const selectedComponent = ref(props.modelValue)
const customInput = ref('')

// 可用的组件列表
const availableComponents = ref([])

// 计算属性
const filteredComponents = computed(() => {
  if (!searchKeyword.value) {
    return availableComponents.value
  }
  return availableComponents.value.filter(component => 
    component.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const loadComponents = () => {
  try {
    availableComponents.value = getAvailableComponents()
  } catch (error) {
    console.warn('无法获取组件列表:', error)
    // 使用默认组件列表
    availableComponents.value = [
      '@/views/Dashboard.vue',
      '@/views/system/MenuManagement.vue',
      '@/views/system/UserTypeManagement.vue'
    ]
  }
}

const getComponentDescriptionLocal = (componentPath) => {
  return getComponentDesc(componentPath)
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleSelectComponent = (componentPath) => {
  selectedComponent.value = componentPath
  customInput.value = componentPath
}

const handleConfirm = () => {
  const finalValue = customInput.value || selectedComponent.value
  emit('update:modelValue', finalValue)
  dialogVisible.value = false
}

const handleClear = () => {
  emit('update:modelValue', '')
  dialogVisible.value = false
}

const handleCancel = () => {
  // 取消时恢复原值，不修改外部值
  selectedComponent.value = props.modelValue
  customInput.value = props.modelValue
  dialogVisible.value = false
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedComponent.value = newValue
  customInput.value = newValue
})

// 监听对话框打开
watch(dialogVisible, (visible) => {
  if (visible) {
    customInput.value = selectedComponent.value
  }
})

// 组件挂载时加载组件列表
onMounted(() => {
  loadComponents()
})
</script>

<style lang="scss" scoped>
.component-selector {
  width: 100%;

  .cursor-pointer {
    cursor: pointer;
  }

  .component-icon {
    color: var(--success-color);
    font-size: 16px;
  }

  .el-input {
    cursor: pointer;

    &:hover {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--primary-color) inset;
      }
    }
  }
}

.component-selector-content {
  .search-section {
    margin-bottom: 16px;
  }

  .component-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
    background: var(--bg-color-page);
    border-radius: 8px;

    .component-item {
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 8px;
      margin-bottom: 8px;
      background: var(--bg-color-card);
      border: 1px solid var(--border-color);

      &:last-child {
        margin-bottom: 0;
      }

      &:hover {
        background: var(--bg-color-hover);
        border-color: var(--border-color-dark);
        transform: translateY(-1px);
        box-shadow: var(--box-shadow-base);
      }

      &.active {
        background: var(--primary-lightest);
        border-color: var(--primary-color);
        box-shadow: var(--box-shadow-focus);
      }

      .component-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .component-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 8px;
          background: var(--success-lightest);
          color: var(--success-color);
          font-size: 18px;
          flex-shrink: 0;
        }

        .component-details {
          flex: 1;
          min-width: 0;

          .component-path {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            color: var(--success-color);
            font-weight: 500;
            margin-bottom: 4px;
            word-break: break-all;
          }

          .component-description {
            font-size: 12px;
            color: var(--text-color-secondary);
            line-height: 1.4;
          }
        }
      }

      .component-actions {
        .check-icon {
          color: var(--success-color);
          font-size: 20px;
          animation: checkIn 0.3s ease;
        }
      }

      &.active {
        .component-info {
          .component-icon {
            background: var(--primary-lightest);
            color: var(--primary-color);
          }

          .component-details {
            .component-path {
              color: var(--primary-color);
            }

            .component-description {
              color: var(--primary-color);
            }
          }
        }
      }
    }
  }

  .custom-input-section {
    margin-top: 16px;

    .el-divider {
      margin: 16px 0 12px;
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}

// 动画效果
@keyframes checkIn {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
