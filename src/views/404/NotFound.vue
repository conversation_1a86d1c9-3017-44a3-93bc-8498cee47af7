<template>
  <div class="not-found-container">
    <el-result
      icon="warning"
      title="404"
      sub-title="抱歉，您访问的页面不存在"
    >
      <template #extra>
        <el-space>
          <el-button type="primary" @click="goHome">
            返回首页
          </el-button>
          <el-button @click="goBack">
            返回上一页
          </el-button>
        </el-space>
      </template>
    </el-result>
    
    <div class="suggestions">
      <h3>可能的原因：</h3>
      <ul>
        <li>您输入的网址有误</li>
        <li>该页面已被删除或移动</li>
        <li>您没有访问该页面的权限</li>
        <li>服务器暂时无法响应</li>
      </ul>
      
      <h3>建议您：</h3>
      <ul>
        <li>检查网址是否正确</li>
        <li>返回首页重新导航</li>
        <li>联系系统管理员</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.not-found-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 20px;
  background: linear-gradient(135deg, var(--bg-color-page) 0%, var(--border-color-light) 100%);
}

.el-result {
  width: 100%;
  max-width: 600px;
  border-radius: 20px;
}

.suggestions {
  width: 100%;
  max-width: 600px;
  margin-top: 40px;
  text-align: left;
  background: var(--bg-color-card);
  padding: 24px;
  border-radius: 12px;
  box-shadow: var(--box-shadow-base);
}

.suggestions h3 {
  margin: 20px 0 10px 0;
  color: var(--text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.suggestions h3:first-child {
  margin-top: 0;
}

.suggestions ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.suggestions li {
  margin: 8px 0;
  color: var(--text-color-regular);
  line-height: 1.6;
}

/* 移动端适配 (< 768px) */
@media (max-width: 767px) {
  .not-found-container {
    padding: 16px;
    min-height: 100vh;
  }

  .el-result {
    width: 100%;
    max-width: none;
  }

  /* 调整 Element Plus Result 组件内部样式 */
  .el-result :deep(.el-result__icon) {
    font-size: 48px !important;
  }

  .el-result :deep(.el-result__title) {
    font-size: 24px !important;
    margin: 16px 0 8px 0 !important;
  }

  .el-result :deep(.el-result__subtitle) {
    font-size: 14px !important;
    margin-bottom: 20px !important;
  }

  .el-result :deep(.el-result__extra) {
    margin-top: 20px !important;
  }

  /* 按钮在移动端垂直排列 */
  .el-result :deep(.el-space) {
    flex-direction: column !important;
    width: 100% !important;
  }

  .el-result :deep(.el-space .el-space__item) {
    margin-right: 0 !important;
    margin-bottom: 12px !important;
    width: 100% !important;
  }

  .el-result :deep(.el-space .el-space__item:last-child) {
    margin-bottom: 0 !important;
  }

  .el-result :deep(.el-button) {
    width: 100% !important;
    height: 44px !important;
    font-size: 16px !important;
  }

  .suggestions {
    width: 100%;
    max-width: none;
    margin-top: 24px;
    padding: 20px;
    border-radius: 8px;
  }

  .suggestions h3 {
    font-size: 15px;
    margin: 16px 0 8px 0;
  }

  .suggestions h3:first-child {
    margin-top: 0;
  }

  .suggestions ul {
    padding-left: 16px;
    margin-bottom: 16px;
  }

  .suggestions li {
    margin: 6px 0;
    font-size: 14px;
    line-height: 1.5;
  }
}

/* 平板端适配 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .not-found-container {
    padding: 24px;
  }

  .el-result {
    width: 80%;
    max-width: 500px;
  }

  .suggestions {
    width: 80%;
    max-width: 500px;
    margin-top: 32px;
    padding: 20px;
  }

  .suggestions h3 {
    font-size: 15px;
  }

  .suggestions li {
    font-size: 14px;
  }
}

/* 桌面端 (>= 1024px) */
@media (min-width: 1024px) {
  .el-result {
    width: 50%;
    max-width: 600px;
  }

  .suggestions {
    width: 50%;
    max-width: 600px;
  }
}
</style>
