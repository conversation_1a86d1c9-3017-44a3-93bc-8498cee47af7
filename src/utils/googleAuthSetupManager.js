/**
 * 谷歌验证码设置提示管理器
 * 用于检查用户是否已设置谷歌验证码，并在需要时显示设置提示弹窗
 */

import { createApp } from 'vue'
import { ElMessage } from 'element-plus'
import ElementPlus from 'element-plus'
import GoogleAuthSetupDialog from '@/components/GoogleAuthSetupDialog.vue'

class GoogleAuthSetupManager {
  constructor() {
    this.dialogInstance = null
    this.dialogApp = null
    this.isDialogVisible = false
    this.hasChecked = false // 防止重复检查
  }

  /**
   * 初始化设置提示弹窗
   */
  init() {
    if (this.dialogInstance) return

    // 创建弹窗容器
    const container = document.createElement('div')
    container.id = 'google-auth-setup-dialog-container'
    document.body.appendChild(container)

    // 创建Vue应用实例
    this.dialogApp = createApp(GoogleAuthSetupDialog, {
      onSetup: () => this.handleSetup(),
      onCancel: () => this.handleCancel()
    })

    // 注册Element Plus
    this.dialogApp.use(ElementPlus)

    // 挂载应用
    this.dialogInstance = this.dialogApp.mount(container)

    // 确保初始化时弹窗是隐藏的
    this.dialogInstance.hide()
  }

  /**
   * 检查用户是否需要设置谷歌验证码
   * @param {Object} userInfo 用户信息对象
   * @param {boolean} force 是否强制检查（忽略hasChecked标志）
   * @returns {boolean} 是否显示了设置提示
   */
  checkGoogleAuthSetup(userInfo, force = false) {
    // 如果已经检查过且不是强制检查，则跳过
    if (this.hasChecked && !force) {
      return false
    }

    // 如果用户信息不存在，跳过检查
    if (!userInfo || typeof userInfo !== 'object') {
      return false
    }

    // 检查hasGoogleAuth字段
    const hasGoogleAuth = userInfo.hasGoogleAuth

    // 如果已经设置了谷歌验证码，不需要提示
    if (hasGoogleAuth === true) {
      this.hasChecked = true
      return false
    }

    // 如果hasGoogleAuth为false或不存在，显示设置提示
    if (hasGoogleAuth === false || hasGoogleAuth === undefined || hasGoogleAuth === null) {
      this.showSetupDialog()
      this.hasChecked = true
      return true
    }

    return false
  }

  /**
   * 显示设置提示弹窗
   */
  showSetupDialog() {
    // 懒加载：只在需要时才初始化
    if (!this.dialogInstance) {
      this.init()
    }

    // 如果弹窗已经显示，不重复显示
    if (this.isDialogVisible) {
      return
    }

    this.isDialogVisible = true
    this.dialogInstance.show()
    
    console.log('🔐 显示谷歌验证码设置提示弹窗')
  }

  /**
   * 隐藏设置提示弹窗
   */
  hideSetupDialog() {
    if (this.dialogInstance) {
      this.dialogInstance.hide()
    }
    this.isDialogVisible = false
  }

  /**
   * 处理用户选择立即设置
   */
  handleSetup() {
    console.log('🔐 用户选择立即设置谷歌验证码')
    this.hideSetupDialog()
    
    // 跳转到个人中心的安全设置页面
    this.navigateToSecuritySettings()
  }

  /**
   * 处理用户选择取消/稍后设置
   */
  handleCancel() {
    console.log('🔐 用户选择稍后设置谷歌验证码')
    this.hideSetupDialog()

    // 可以在这里记录用户的选择，比如设置一个延迟提醒
    this.recordUserChoice('later')
  }

  /**
   * 跳转到安全设置页面
   */
  navigateToSecuritySettings() {
    // 动态导入router，避免循环依赖
    import('@/router/index.js').then(({ default: router }) => {
      // 跳转到个人中心的安全设置页面
      router.push('/profile?tab=security').catch(err => {
        console.warn('跳转到安全设置页面失败:', err)
        // 如果路由跳转失败，显示提示信息
        ElMessage.info('请前往个人中心 > 安全设置 中配置谷歌验证码')
      })
    }).catch(err => {
      console.error('导入router失败:', err)
      ElMessage.info('请前往个人中心 > 安全设置 中配置谷歌验证码')
    })
  }

  /**
   * 记录用户的选择
   * @param {string} choice 用户选择：'setup' | 'later'
   */
  recordUserChoice(choice) {
    try {
      const timestamp = Date.now()
      const record = {
        choice,
        timestamp,
        date: new Date().toISOString()
      }

      // 保存到localStorage，用于后续的提醒逻辑
      localStorage.setItem('google_auth_setup_choice', JSON.stringify(record))

      // 如果用户选择稍后设置，可以设置一个提醒间隔（比如1天后再提醒）
      if (choice === 'later') {
        const nextReminder = timestamp + (1 * 24 * 60 * 60 * 1000) // 1天后
        localStorage.setItem('google_auth_next_reminder', nextReminder.toString())
      }
    } catch (error) {
      console.warn('记录用户选择失败:', error)
    }
  }

  /**
   * 检查是否应该显示提醒（基于用户之前的选择）
   * @returns {boolean} 是否应该显示提醒
   */
  shouldShowReminder() {
    try {
      const nextReminderTime = localStorage.getItem('google_auth_next_reminder')
      if (!nextReminderTime) {
        return true // 没有记录，应该显示
      }

      const now = Date.now()
      const reminderTime = parseInt(nextReminderTime, 10)

      return now >= reminderTime // 如果当前时间超过了提醒时间，应该显示
    } catch (error) {
      console.warn('检查提醒时间失败:', error)
      return true // 出错时默认显示
    }
  }

  /**
   * 重置检查状态（用于重新检查）
   */
  resetCheckStatus() {
    this.hasChecked = false
  }

  /**
   * 清除用户选择记录
   */
  clearUserChoice() {
    try {
      localStorage.removeItem('google_auth_setup_choice')
      localStorage.removeItem('google_auth_next_reminder')
      this.resetCheckStatus()
    } catch (error) {
      console.warn('清除用户选择记录失败:', error)
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    // 销毁Vue应用实例
    if (this.dialogApp) {
      this.dialogApp.unmount()
      this.dialogApp = null
    }

    // 移除DOM容器
    const container = document.getElementById('google-auth-setup-dialog-container')
    if (container) {
      document.body.removeChild(container)
    }

    this.dialogInstance = null
    this.isDialogVisible = false
    this.hasChecked = false
  }
}

// 创建全局单例
const googleAuthSetupManager = new GoogleAuthSetupManager()

export default googleAuthSetupManager
