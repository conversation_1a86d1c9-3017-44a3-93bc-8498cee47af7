/**
 * 移动端适配公共样式
 * 提供统一的移动端布局和交互样式
 */

/* ========== 管理页面移动端适配 ========== */

/* 搜索表单移动端样式 */
@media (max-width: 768px) {
  .management-page {
    .search-section {
      padding: 16px;
    }
    
    .search-form {
      display: block;
      
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 0;
        display: block;
        width: 100%;
        
        .el-form-item__label {
          display: block;
          width: 100%;
          text-align: left;
          margin-bottom: 8px;
          font-weight: 500;
          color: var(--text-color-primary);
        }
        
        .el-form-item__content {
          margin-left: 0 !important;
          width: 100%;
        }
        
        /* 输入框和选择框全宽 */
        .el-input,
        .el-select,
        .el-date-editor {
          width: 100%;
        }
      }
      
      /* 搜索和重置按钮行 */
      .el-form-item:last-child {
        margin-bottom: 0;
        
        .el-form-item__content {
          display: flex;
          gap: 12px;
          
          .el-button {
            flex: 1;
            height: 44px;
            font-size: 16px;
            border-radius: 8px;
          }
        }
      }
    }
    
    /* 操作按钮区域移动端样式 */
    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-top: 16px;
      
      .el-button {
        width: 100%;
        height: 44px;
        font-size: 16px;
        border-radius: 8px;
        margin: 0;
        
        .el-icon {
          margin-right: 8px;
        }
      }
      
      .el-dropdown {
        width: 100%;
        
        .el-button {
          width: 100%;
          justify-content: center;
        }
      }
    }
    
    /* 表格区域移动端优化 */
    .table-section {
      margin-top: 16px;
      
      .el-table {
        font-size: 14px;
        
        .el-table__header {
          font-size: 13px;
        }
        
        .el-table__body {
          font-size: 13px;
        }
      }
    }
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .management-page {
    .search-section {
      padding: 12px;
    }
    
    .search-form {
      .el-form-item {
        margin-bottom: 14px;
        
        .el-form-item__label {
          margin-bottom: 6px;
          font-size: 14px;
        }
      }
      
      /* 搜索和重置按钮在超小屏幕上 */
      .el-form-item:last-child {
        .el-form-item__content {
          .el-button {
            height: 40px;
            font-size: 14px;
          }
        }
      }
    }
    
    .action-buttons {
      gap: 10px;
      margin-top: 12px;
      
      .el-button {
        height: 40px;
        font-size: 14px;
      }
    }
  }
}

/* ========== 对话框移动端适配 ========== */

@media (max-width: 768px) {
  /* 详情对话框移动端适配 */
  .detail-dialog {
    .detail-content {
      padding: 0 8px;
    }
    
    /* 移动端描述列表单列显示 */
    :deep(.el-descriptions) {
      .el-descriptions__table {
        .el-descriptions__row {
          .el-descriptions__cell {
            width: 100% !important;
            display: block !important;
            
            &.is-bordered-label {
              width: 100% !important;
              text-align: left !important;
              padding: 12px 16px 8px 16px !important;
              background: var(--bg-color-soft) !important;
              font-weight: 600;
              border-bottom: none !important;
            }
            
            &.is-bordered-content {
              width: 100% !important;
              padding: 8px 16px 12px 16px !important;
              border-top: none !important;
              border-bottom: 1px solid var(--border-color-lighter) !important;
            }
          }
        }
      }
    }
    
    /* 移动端分割线样式 */
    :deep(.el-divider) {
      margin: 24px 0 16px 0;
      
      .el-divider__text {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color-primary);
        background: var(--bg-color-container);
        padding: 0 16px;
      }
    }
    
    .dialog-footer {
      flex-direction: column-reverse;
      gap: 8px;
      padding: 16px;
      
      .el-button {
        width: 100%;
        margin: 0;
      }
    }
  }
}

/* 平板适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .detail-dialog {
    .detail-content {
      padding: 0 12px;
    }
    
    :deep(.el-descriptions) {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 12px 16px !important;
        }
      }
    }
  }
}

/* ========== 通用移动端工具类 ========== */

/* 移动端隐藏 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
}

/* 桌面端隐藏 */
@media (min-width: 769px) {
  .desktop-hidden {
    display: none !important;
  }
}

/* 移动端全宽 */
@media (max-width: 768px) {
  .mobile-full-width {
    width: 100% !important;
  }
}

/* 移动端垂直布局 */
@media (max-width: 768px) {
  .mobile-vertical {
    flex-direction: column !important;
    
    > * {
      width: 100% !important;
      margin-right: 0 !important;
      margin-bottom: 12px !important;
    }
    
    > *:last-child {
      margin-bottom: 0 !important;
    }
  }
}
