<template>
  <div class="pagination-wrapper">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="currentPageSize"
      :total="total"
      :page-sizes="pageSizes"
      :layout="currentLayout"
      :pager-count="pagerCount"
      :background="background"
      :size="currentSize"
      :disabled="disabled"
      :hide-on-single-page="hideOnSinglePage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'

// Props
const props = defineProps({
  // 当前页码
  current: {
    type: Number,
    default: 1
  },
  // 每页显示条数
  pageSize: {
    type: Number,
    default: 10
  },
  // 总条数
  total: {
    type: Number,
    default: 0
  },
  // 每页显示个数选择器的选项设置
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  // 组件布局，子组件名用逗号分隔
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  // 是否为分页按钮添加背景色
  background: {
    type: Boolean,
    default: true
  },
  // 分页组件尺寸
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small'].includes(value)
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 只有一页时是否隐藏
  hideOnSinglePage: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:current', 'update:size', 'size-change', 'current-change'])

// 响应式状态
const isMobile = ref(false)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const currentPage = computed({
  get: () => props.current,
  set: (value) => emit('update:current', value)
})

const currentPageSize = computed({
  get: () => props.pageSize,
  set: (value) => emit('update:pageSize', value)
})

// 动态布局
const currentLayout = computed(() => {
  return isMobile.value ? 'prev, pager, next' : props.layout
})

const pagerCount = computed(() => {
  return isMobile.value ? 3 : 5
})

// 动态尺寸
const currentSize = computed(() => {
  return isMobile.value ? 'small' : props.size
})

// 事件处理
const handleSizeChange = (size) => {
  emit('update:pageSize', size)
  emit('size-change', size)
}

const handleCurrentChange = (current) => {
  emit('update:current', current)
  emit('current-change', current)
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
  padding: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .pagination-wrapper {
    justify-content: center;
    padding: 16px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .pagination-wrapper {
    padding: 12px;
  }
}
</style>
