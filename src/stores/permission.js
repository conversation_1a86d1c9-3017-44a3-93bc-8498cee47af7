import { defineStore } from 'pinia'
import { ref } from 'vue'
import { permissionApi } from '@/api'

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const permissions = ref([])
  const loading = ref(false)
  const isLoaded = ref(false) // 权限是否已加载

  // 缓存正在进行的权限获取请求，避免重复请求
  let fetchPromise = null

  /**
   * 获取用户权限列表
   * @returns {Promise<Array<string>>}
   */
  const fetchPermissions = async () => {
    // 如果已经在加载中，返回正在进行的请求
    if (fetchPromise) {
      return fetchPromise
    }

    // 如果已经加载过，直接返回
    if (isLoaded.value) {
      return permissions.value
    }

    // 创建新的请求
    fetchPromise = (async () => {
      try {
        loading.value = true
        const response = await permissionApi.getUserPermissions()
        permissions.value = response || []
        isLoaded.value = true
        return permissions.value
      } catch (error) {
        console.error('获取用户权限失败:', error)
        // 不显示错误消息，由路由守卫统一处理
        permissions.value = []
        isLoaded.value = false
        throw error
      } finally {
        loading.value = false
        fetchPromise = null // 清除请求缓存
      }
    })()

    return fetchPromise
  }

  /**
   * 检查是否有指定权限
   * @param {string} permission 权限字符串
   * @returns {boolean}
   */
  const hasPermission = (permission) => {
    if (!permission) return true
    return permissions.value.includes(permission)
  }

  /**
   * 检查是否有任意一个权限
   * @param {Array<string>} permissionList 权限数组
   * @returns {boolean}
   */
  const hasAnyPermission = (permissionList) => {
    if (!permissionList || permissionList.length === 0) return true
    return permissionList.some(permission => hasPermission(permission))
  }

  /**
   * 检查是否有所有权限
   * @param {Array<string>} permissionList 权限数组
   * @returns {boolean}
   */
  const hasAllPermissions = (permissionList) => {
    if (!permissionList || permissionList.length === 0) return true
    return permissionList.every(permission => hasPermission(permission))
  }

  /**
   * 清除权限数据
   */
  const clearPermissions = () => {
    permissions.value = []
    isLoaded.value = false
    fetchPromise = null // 清除请求缓存
  }

  /**
   * 设置权限数据（用于测试或其他场景）
   * @param {Array<string>} newPermissions 新的权限数组
   */
  const setPermissions = (newPermissions) => {
    permissions.value = newPermissions || []
    isLoaded.value = true // 设置权限后标记为已加载
  }

  return {
    // 状态
    permissions,
    loading,
    isLoaded,

    // 方法
    fetchPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    clearPermissions,
    setPermissions
  }
})
