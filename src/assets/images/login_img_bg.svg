<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 透明背景，去除白边 -->
  
  <!-- 数据图表背景 -->
  <rect x="50" y="50" width="120" height="80" rx="8" fill="#ffffff" stroke="#409EFF" stroke-width="2"/>
  
  <!-- 图表线条 -->
  <polyline points="60,110 80,90 100,100 120,80 140,85 160,75" stroke="#409EFF" stroke-width="3" fill="none"/>
  <polyline points="60,120 80,105 100,115 120,95 140,100 160,90" stroke="#67C23A" stroke-width="2" fill="none"/>
  
  <!-- 数据点 -->
  <circle cx="80" cy="90" r="3" fill="#409EFF"/>
  <circle cx="100" cy="100" r="3" fill="#409EFF"/>
  <circle cx="120" cy="80" r="3" fill="#409EFF"/>
  <circle cx="140" cy="85" r="3" fill="#409EFF"/>
  
  <!-- 人物1 - 主要角色 -->
  <g transform="translate(200, 80)">
    <!-- 头部 -->
    <circle cx="0" cy="0" r="20" fill="#FFE4B5"/>
    <!-- 头发 -->
    <path d="M-18,-8 Q-20,-25 0,-25 Q20,-25 18,-8" fill="#8B4513"/>
    <!-- 眼睛 -->
    <circle cx="-6" cy="-3" r="2" fill="#333"/>
    <circle cx="6" cy="-3" r="2" fill="#333"/>
    <!-- 嘴巴 -->
    <path d="M-5,5 Q0,8 5,5" stroke="#333" stroke-width="1" fill="none"/>
    
    <!-- 身体 -->
    <rect x="-15" y="20" width="30" height="40" rx="15" fill="#409EFF"/>
    <!-- 手臂 -->
    <rect x="-25" y="25" width="10" height="25" rx="5" fill="#FFE4B5"/>
    <rect x="15" y="25" width="10" height="25" rx="5" fill="#FFE4B5"/>
    <!-- 腿部 -->
    <rect x="-10" y="60" width="8" height="30" rx="4" fill="#2C3E50"/>
    <rect x="2" y="60" width="8" height="30" rx="4" fill="#2C3E50"/>
  </g>
  
  <!-- 人物2 - 协作伙伴 -->
  <g transform="translate(280, 100)">
    <!-- 头部 -->
    <circle cx="0" cy="0" r="18" fill="#FFE4B5"/>
    <!-- 头发 -->
    <path d="M-16,-6 Q-18,-22 0,-22 Q18,-22 16,-6" fill="#654321"/>
    <!-- 眼睛 -->
    <circle cx="-5" cy="-2" r="1.5" fill="#333"/>
    <circle cx="5" cy="-2" r="1.5" fill="#333"/>
    <!-- 嘴巴 -->
    <path d="M-4,4 Q0,7 4,4" stroke="#333" stroke-width="1" fill="none"/>
    
    <!-- 身体 -->
    <rect x="-12" y="18" width="24" height="35" rx="12" fill="#67C23A"/>
    <!-- 手臂 -->
    <rect x="-20" y="22" width="8" height="20" rx="4" fill="#FFE4B5"/>
    <rect x="12" y="22" width="8" height="20" rx="4" fill="#FFE4B5"/>
    <!-- 腿部 -->
    <rect x="-8" y="53" width="6" height="25" rx="3" fill="#2C3E50"/>
    <rect x="2" y="53" width="6" height="25" rx="3" fill="#2C3E50"/>
  </g>
  
  <!-- 白板/屏幕 -->
  <rect x="230" y="40" width="80" height="50" rx="4" fill="#ffffff" stroke="#E4E7ED" stroke-width="2"/>
  
  <!-- 白板内容 - 数据可视化 -->
  <rect x="240" y="50" width="60" height="30" fill="#F5F7FA"/>
  <!-- 柱状图 -->
  <rect x="245" y="65" width="6" height="10" fill="#409EFF"/>
  <rect x="255" y="60" width="6" height="15" fill="#67C23A"/>
  <rect x="265" y="55" width="6" height="20" fill="#E6A23C"/>
  <rect x="275" y="62" width="6" height="13" fill="#F56C6C"/>
  <rect x="285" y="58" width="6" height="17" fill="#909399"/>
  
  <!-- 连接线和箭头 -->
  <path d="M170,90 Q190,85 210,90" stroke="#409EFF" stroke-width="2" fill="none" stroke-dasharray="5,5"/>
  <polygon points="210,88 215,90 210,92" fill="#409EFF"/>
  
  <!-- 数据流动效果 -->
  <g opacity="0.6">
    <circle cx="180" cy="120" r="3" fill="#409EFF">
      <animate attributeName="cx" values="180;220;260" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.5;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="160" cy="130" r="2" fill="#67C23A">
      <animate attributeName="cx" values="160;200;240" dur="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="1;0.5;0" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 装饰性元素 -->
  <circle cx="350" cy="60" r="8" fill="#409EFF" opacity="0.3"/>
  <circle cx="370" cy="80" r="5" fill="#67C23A" opacity="0.4"/>
  <circle cx="360" cy="100" r="6" fill="#E6A23C" opacity="0.3"/>
  
  <!-- 云朵 -->
  <g transform="translate(320, 30)" opacity="0.5">
    <circle cx="0" cy="0" r="8" fill="#ffffff"/>
    <circle cx="10" cy="0" r="10" fill="#ffffff"/>
    <circle cx="20" cy="0" r="8" fill="#ffffff"/>
    <circle cx="5" cy="-8" r="6" fill="#ffffff"/>
    <circle cx="15" cy="-8" r="6" fill="#ffffff"/>
  </g>
  
  <!-- 文档图标 -->
  <g transform="translate(60, 160)">
    <rect x="0" y="0" width="20" height="25" rx="2" fill="#ffffff" stroke="#409EFF"/>
    <line x1="3" y1="5" x2="17" y2="5" stroke="#409EFF" stroke-width="1"/>
    <line x1="3" y1="9" x2="17" y2="9" stroke="#409EFF" stroke-width="1"/>
    <line x1="3" y1="13" x2="12" y2="13" stroke="#409EFF" stroke-width="1"/>
  </g>
  
  <!-- 齿轮图标 -->
  <g transform="translate(320, 180)">
    <circle cx="0" cy="0" r="12" fill="none" stroke="#409EFF" stroke-width="2"/>
    <circle cx="0" cy="0" r="6" fill="#409EFF"/>
    <rect x="-2" y="-15" width="4" height="6" fill="#409EFF"/>
    <rect x="-2" y="9" width="4" height="6" fill="#409EFF"/>
    <rect x="-15" y="-2" width="6" height="4" fill="#409EFF"/>
    <rect x="9" y="-2" width="6" height="4" fill="#409EFF"/>
  </g>
</svg>
