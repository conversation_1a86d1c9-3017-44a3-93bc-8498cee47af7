# 登录日志API文档

## 概述

登录日志模块提供了完整的用户登录行为记录、查询、统计和分析功能。支持安全审计、风险评估和可疑行为检测。

## 基础信息

- **基础路径**: `/system/login-log`
- **权限前缀**: `login:log`
- **支持格式**: JSON
- **时间格式**: `yyyy-MM-dd HH:mm:ss`
- **权限模式**: 支持权限验证或超级管理员角色

## API接口列表

### 1. 分页查询登录日志

**接口地址**: `GET /system/login-log/page`

**权限要求**: `login:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| userId | String | 否 | - | 用户ID |
| username | String | 否 | - | 用户名 |
| clientIp | String | 否 | - | 客户端IP |
| loginStatus | Integer | 否 | - | 登录状态(0-失败,1-成功) |
| isSuspicious | Integer | 否 | - | 是否可疑(0-否,1-是) |
| riskLevel | String | 否 | - | 风险等级(LOW/MEDIUM/HIGH) |
| startTime | DateTime | 否 | - | 开始时间 |
| endTime | DateTime | 否 | - | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  },
  "success": true
}
```

### 2. 获取用户最近登录记录

**接口地址**: `GET /system/login-log/recent/{userId}`

**权限要求**: `login:log:view` 或超级管理员角色

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | String | 是 | 用户ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 10 | 限制数量 |

### 3. 获取登录统计信息

**接口地址**: `GET /system/login-log/statistics`

**权限要求**: `login:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| startTime | DateTime | 否 | 7天前 | 开始时间 |
| endTime | DateTime | 否 | 当前时间 | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalLogins": 1000,
    "successLogins": 950,
    "failureLogins": 50,
    "successRate": 95.0,
    "uniqueUsers": 100,
    "uniqueIps": 200,
    "suspiciousLogins": 5,
    "highRiskLogins": 2
  },
  "success": true
}
```

### 4. 统计用户登录次数

**接口地址**: `GET /system/login-log/count/user/{userId}`

**权限要求**: `login:log:view` 或超级管理员角色

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | String | 是 | 用户ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| startTime | DateTime | 否 | 7天前 | 开始时间 |
| endTime | DateTime | 否 | 当前时间 | 结束时间 |
| loginStatus | Integer | 否 | - | 登录状态 |

### 5. 统计IP地址登录次数

**接口地址**: `GET /system/login-log/count/ip/{clientIp}`

**权限要求**: `login:log:view` 或超级管理员角色

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| clientIp | String | 是 | 客户端IP |

**请求参数**: 同用户登录次数统计接口

### 6. 获取登录日志详情

**接口地址**: `GET /system/login-log/{id}`

**权限要求**: `login:log:view` 或超级管理员角色

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | String | 是 | 登录日志ID |

### 7. 清理过期登录日志

**接口地址**: `DELETE /system/login-log/cleanup`

**权限要求**: `login:log:delete` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 30 | 清理多少天前的记录 |

**响应示例**:
```json
{
  "code": 200,
  "message": "清理成功",
  "data": 150,
  "success": true
}
```

### 8. 清空所有登录日志

**接口地址**: `DELETE /system/login-log/clear`

**权限要求**: `login:log:delete` 或超级管理员角色

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "清空成功",
  "data": 856,
  "success": true
}
```

**注意事项**:
- 此操作将清空所有登录日志记录
- 操作不可逆，请谨慎使用
- 建议在系统维护或重置时使用
- 操作会被记录到操作日志中

### 9. 获取登录趋势数据

**接口地址**: `GET /system/login-log/trend`

**权限要求**: `login:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 7 | 统计天数 |

### 10. 获取热门登录IP地址

**接口地址**: `GET /system/login-log/top-ips`

**权限要求**: `login:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 10 | 限制数量 |
| days | Integer | 否 | 7 | 统计天数 |

### 11. 获取登录设备类型统计

**接口地址**: `GET /system/login-log/device-stats`

**权限要求**: `login:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 7 | 统计天数 |

### 12. 获取地理位置统计

**接口地址**: `GET /system/login-log/location-stats`

**权限要求**: `login:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 7 | 统计天数 |

## 数据模型

### LoginLog 实体

```json
{
  "id": "登录日志ID",
  "userId": "用户ID",
  "username": "用户名",
  "loginTime": "登录时间",
  "loginStatus": "登录状态(0-失败,1-成功)",
  "failureReason": "失败原因",
  "sessionId": "会话ID",
  "clientIp": "客户端IP",
  "realIp": "真实IP",
  "isProxy": "是否代理(0-否,1-是)",
  "location": "地理位置(格式：国家 省份 城市)",
  "userAgent": "用户代理",
  "deviceType": "设备类型",
  "osName": "操作系统",
  "browserName": "浏览器",
  "riskLevel": "风险等级",
  "isSuspicious": "是否可疑(0-否,1-是)",
  "suspiciousReasons": "可疑原因列表",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 查询最近7天的登录统计
```bash
curl -X GET "http://localhost:8080/system/login-log/statistics" \
  -H "Authorization: Bearer your-token"
```

### 查询用户登录记录
```bash
curl -X GET "http://localhost:8080/system/login-log/page?userId=123&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 查询可疑登录
```bash
curl -X GET "http://localhost:8080/system/login-log/page?isSuspicious=1&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 查询高风险登录
```bash
curl -X GET "http://localhost:8080/system/login-log/page?riskLevel=HIGH&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 查询可疑且高风险的登录
```bash
curl -X GET "http://localhost:8080/system/login-log/page?isSuspicious=1&riskLevel=HIGH&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 获取热门IP地址
```bash
curl -X GET "http://localhost:8080/system/login-log/top-ips?limit=10&days=7" \
  -H "Authorization: Bearer your-token"
```

### 获取设备类型统计
```bash
curl -X GET "http://localhost:8080/system/login-log/device-stats?days=7" \
  -H "Authorization: Bearer your-token"
```

### 获取登录趋势数据
```bash
curl -X GET "http://localhost:8080/system/login-log/trend?days=7" \
  -H "Authorization: Bearer your-token"
```

### 清理过期登录日志
```bash
curl -X DELETE "http://localhost:8080/system/login-log/cleanup?days=30" \
  -H "Authorization: Bearer your-token"
```

### 清空所有登录日志
```bash
curl -X DELETE "http://localhost:8080/system/login-log/clear" \
  -H "Authorization: Bearer your-token"
```
