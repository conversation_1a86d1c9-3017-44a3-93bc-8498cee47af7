<template>
  <!-- 谷歌验证码设置提示弹窗 -->
  <teleport to="body">
    <transition name="modal-fade">
      <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
        <transition name="modal-slide">
          <div v-if="visible" class="modal-container" @click.stop>
            <div class="setup-container">
              <!-- 头部区域 -->
              <div class="setup-header">
                <div class="setup-icon-wrapper">
                  <div class="setup-icon">
                    <svg viewBox="0 0 48 48" width="36" height="36">
                      <!-- 外层盾牌 -->
                      <path fill="#f87171" d="M24 2L8 8v12c0 10 6.5 19.5 16 22 9.5-2.5 16-12 16-22V8L24 2z"/>
                      <!-- 内层高光 -->
                      <path fill="#fca5a5" d="M24 6L12 10v10c0 7.5 5 14.5 12 16.5 7-2 12-9 12-16.5V10L24 6z"/>
                      <!-- 锁图标 -->
                      <rect x="18" y="20" width="12" height="8" rx="1" fill="#ffffff" opacity="0.9"/>
                      <path d="M20 18v-2c0-2.2 1.8-4 4-4s4 1.8 4 4v2" stroke="#ffffff" stroke-width="2" fill="none" opacity="0.9"/>
                      <!-- 锁孔 -->
                      <circle cx="24" cy="24" r="1.5" fill="#f87171"/>
                    </svg>
                  </div>
                </div>
                <h2 class="setup-title">安全提醒</h2>
                <p class="setup-subtitle">您尚未设置谷歌验证码</p>
              </div>

              <!-- 内容区域 -->
              <div class="setup-content">
                <div class="security-benefits">
                  <div class="benefit-item">
                    <svg class="benefit-icon" viewBox="0 0 20 20" width="18" height="18">
                      <path fill="currentColor" d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                      <path fill="currentColor" fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                    </svg>
                    <span>提高账户安全性，防止未授权访问</span>
                  </div>
                  <div class="benefit-item">
                    <svg class="benefit-icon" viewBox="0 0 20 20" width="18" height="18">
                      <path fill="currentColor" fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                    </svg>
                    <span>双重身份验证，即使密码泄露也能保护账户</span>
                  </div>
                  <div class="benefit-item">
                    <svg class="benefit-icon" viewBox="0 0 20 20" width="18" height="18">
                      <path fill="currentColor" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span>符合企业安全规范，保障数据安全</span>
                  </div>
                </div>
              </div>

              <!-- 按钮区域 -->
              <div class="setup-actions">
                <button
                  class="btn btn-cancel"
                  @click="handleCancel"
                >
                  稍后设置
                </button>
                <button
                  class="btn btn-setup"
                  @click="handleSetup"
                >
                  立即设置
                </button>
              </div>

              <!-- 帮助信息 -->
              <div class="setup-help">
                <div class="help-item">
                  <svg class="help-icon" viewBox="0 0 20 20" width="14" height="14">
                    <path fill="currentColor" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"/>
                  </svg>
                  <span>设置后可在个人中心管理谷歌验证码</span>
                </div>
              </div>
            </div>
          </div>
        </transition>
      </div>
    </transition>
  </teleport>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'GoogleAuthSetupDialog',
  emits: ['setup', 'cancel'],
  setup(props, { emit }) {
    const visible = ref(false)

    // 显示弹窗
    const show = () => {
      visible.value = true
    }

    // 隐藏弹窗
    const hide = () => {
      visible.value = false
    }

    // 处理设置按钮点击
    const handleSetup = () => {
      emit('setup')
      hide()
    }

    // 处理取消按钮点击
    const handleCancel = () => {
      emit('cancel')
      hide()
    }

    // 处理遮罩层点击
    const handleOverlayClick = () => {
      // 允许点击遮罩层关闭弹窗
      // handleCancel()
    }

    return {
      visible,
      show,
      hide,
      handleSetup,
      handleCancel,
      handleOverlayClick
    }
  }
}
</script>

<style lang="scss" scoped>
// 弹窗遮罩层
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 9000;
  padding: var(--spacing-lg);
  padding-top: 20vh;
}

// 弹窗容器
.modal-container {
  position: relative;
  width: 100%;
  max-width: 480px;
  background: var(--bg-color-card);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid var(--border-color-light);
  overflow: hidden;
}

.setup-container {
  padding: 30px;
  text-align: center;
}

.setup-header {
  margin-bottom: 32px;

  .setup-icon-wrapper {
    margin-bottom: 20px;
    
    .setup-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #f87171, #ef4444);
      border-radius: var(--border-radius-xl);
      box-shadow: 0 8px 20px rgba(248, 113, 113, 0.3);

      svg {
        filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.2));
      }
    }
  }

  .setup-title {
    margin: 0 0 12px 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.3px;
  }

  .setup-subtitle {
    margin: 0;
    font-size: var(--font-size-md);
    color: var(--text-color-secondary);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-relaxed);
  }
}

.setup-content {
  margin-bottom: 32px;
  text-align: left;

  .security-benefits {
    .benefit-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 16px;
      padding: 12px;
      background: var(--bg-color-light);
      border-radius: var(--border-radius-md);
      border-left: 3px solid var(--primary-color);

      &:last-child {
        margin-bottom: 0;
      }

      .benefit-icon {
        flex-shrink: 0;
        margin-top: 2px;
        color: var(--primary-color);
      }

      span {
        font-size: var(--font-size-sm);
        color: var(--text-color);
        line-height: var(--line-height-relaxed);
      }
    }
  }
}

.setup-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;

  .btn {
    flex: 1;
    height: 52px;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--border-radius-lg);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;

    &.btn-cancel {
      background: var(--bg-color-light);
      color: var(--text-color);
      border: 1px solid var(--border-color);

      &:hover {
        background: var(--bg-color-hover);
        border-color: var(--border-color-hover);
      }
    }

    &.btn-setup {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
      color: white;
      box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);

      &:hover {
        background: linear-gradient(135deg, var(--primary-dark), var(--primary-darker));
        box-shadow: 0 6px 16px rgba(var(--primary-color-rgb), 0.4);
        transform: translateY(-1px);
      }
    }
  }
}

.setup-help {
  .help-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    color: var(--text-color-secondary);
    font-size: var(--font-size-xs);

    .help-icon {
      flex-shrink: 0;
    }
  }
}

// 动画效果
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity var(--transition-normal);
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-slide-enter-active,
.modal-slide-leave-active {
  transition: all var(--transition-normal);
}

.modal-slide-enter-from,
.modal-slide-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

// 暗黑模式适配
[data-theme="dark"] {
  .modal-overlay {
    background: rgba(0, 0, 0, 0.7);
  }

  .modal-container {
    background: var(--bg-color-card);
    border-color: var(--border-color);
  }

  .setup-header {
    .setup-title {
      background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .security-benefits .benefit-item {
    background: var(--bg-color-light);
    border-left-color: var(--primary-light);

    .benefit-icon {
      color: var(--primary-light);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .setup-container {
    padding: 24px;
  }

  .setup-header {
    margin-bottom: 28px;

    .setup-icon-wrapper .setup-icon {
      width: 56px;
      height: 56px;
    }

    .setup-title {
      font-size: var(--font-size-xl);
    }
  }

  .setup-actions {
    flex-direction: column;
    gap: 16px;

    .btn {
      height: 64px;
      width: 100%;
    }
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .setup-header {
    margin-bottom: 24px;

    .setup-icon-wrapper .setup-icon {
      width: 48px;
      height: 48px;
    }

    .setup-title {
      font-size: var(--font-size-lg);
    }
  }

  .setup-actions .btn {
    height: 60px;
    font-size: var(--font-size-base);
  }
}
</style>
