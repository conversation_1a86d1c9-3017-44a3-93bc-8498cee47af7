<template>
  <div class="admin-header">
    <div class="header-left">
      <!-- 菜单折叠按钮 -->
      <el-button
        link
        class="sidebar-toggle"
        @click="layoutStore.toggleSidebar"
      >
        <el-icon :size="20">
          <!-- 移动端：根据侧边栏显示状态切换图标 -->
          <template v-if="layoutStore.isMobile">
            <Expand v-if="!layoutStore.isMobileSidebarVisible" />
            <Fold v-else />
          </template>
          <!-- 桌面端：根据折叠状态切换图标 -->
          <template v-else>
            <Fold v-if="!layoutStore.actualCollapsed" />
            <Expand v-else />
          </template>
        </el-icon>
      </el-button>
      
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item
          v-for="(item, index) in breadcrumbList"
          :key="item.id"
          :to="index === breadcrumbList.length - 1 ? undefined : { path: item.path }"
        >
          <!-- <svg-icon
            v-if="item.icon"
            size="small"
            color="var(--text-color-primary)"
            :name="item.icon"
            
            class="breadcrumb-icon"
          /> -->
          {{ item.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <div class="header-right">
      <!-- 搜索按钮 -->
      <el-button link class="header-btn" @click="openMenuSearchDialog">
        <el-icon :size="18">
          <Search />
        </el-icon>
      </el-button>
      
      <!-- 通知 -->
      <el-dropdown class="notification-dropdown">
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <el-button link class="header-btn">
            <el-icon :size="18">
              <Bell />
            </el-icon>
          </el-button>
        </el-badge>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <div class="notification-item">
                <div class="notification-title">系统通知</div>
                <div class="notification-content">您有新的消息</div>
                <div class="notification-time">2分钟前</div>
              </div>
            </el-dropdown-item>
            <el-dropdown-item divided>
              <div class="notification-item">
                <div class="notification-title">任务提醒</div>
                <div class="notification-content">待处理任务：3个</div>
                <div class="notification-time">10分钟前</div>
              </div>
            </el-dropdown-item>
            <el-dropdown-item divided>
              <el-button link size="small" style="width: 100%">
                查看全部通知
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      
      <!-- 全屏 -->
      <el-button link class="header-btn" @click="toggleFullscreen">
        <el-icon :size="18">
          <FullScreen />
        </el-icon>
      </el-button>

      <!-- 主题切换 -->
      <ThemeToggle size="medium" :show-tooltip="true" />

      <!-- 用户信息 -->
      <el-dropdown class="user-dropdown">
        <div class="user-info">
          <UserAvatar :username="displayUsername" :size="32" />
          <span class="username">{{ displayUsername }}</span>
          <el-icon class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="goToProfile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item>
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 菜单搜索弹窗 -->
    <MenuSearchDialog
      v-model="menuSearchDialogVisible"
      @menu-select="handleMenuSelect"
    />
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLayoutStore } from '@/stores/layout'
import { useAuthStore } from '@/stores/auth'

import { generateBreadcrumb } from '@/utils/menu'
import { ElMessageBox } from 'element-plus'
import ThemeToggle from '@/components/ThemeToggle.vue'
import MenuSearchDialog from '@/components/MenuSearchDialog.vue'
import SvgIcon from '@/components/SvgIcon.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import {
  Fold,
  Expand,
  Search,
  Bell,
  FullScreen,
  User,
  Setting,
  SwitchButton,
  ArrowDown,
  Document
} from '@element-plus/icons-vue'

export default {
  name: 'Header',
  components: {
    Fold,
    Expand,
    Search,
    Bell,
    FullScreen,
    User,
    Setting,
    SwitchButton,
    ArrowDown,
    Document,
    ThemeToggle,
    MenuSearchDialog,
    SvgIcon,
    UserAvatar
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const layoutStore = useLayoutStore()
    const authStore = useAuthStore()
    const notificationCount = ref(3)

    // 菜单搜索弹窗相关
    const menuSearchDialogVisible = ref(false)

    const currentRoute = computed(() => route)

    // 显示的用户名
    const displayUsername = computed(() => {
      if (authStore.userInfo && authStore.userInfo.username) {
        return authStore.userInfo.username
      }
      return '用户'
    })

    // 生成面包屑导航
    const breadcrumbList = computed(() => {
      if (route.path === '/dashboard') {
        return [] // 首页不显示额外面包屑
      }
      return generateBreadcrumb(route.path)
    })

    const toggleFullscreen = () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    }

    // 跳转到个人中心
    const goToProfile = () => {
      router.push('/profile')
    }

    // 处理退出登录
    const handleLogout = async () => {
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？退出后需要重新登录才能继续使用系统。',
          '退出登录确认',
          {
            confirmButtonText: '确定退出',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )

        // 用户确认退出，调用退出登录
        await authStore.logout()

        // 跳转到登录页面
        router.push('/login')
      } catch (error) {
        // 用户取消退出或其他错误，不做任何处理
        if (error !== 'cancel') {
          console.error('退出登录失败:', error)
        }
      }
    }

    // 打开菜单搜索弹窗
    const openMenuSearchDialog = () => {
      menuSearchDialogVisible.value = true
    }

    // 处理菜单选择（从子组件接收事件）
    const handleMenuSelect = (item) => {
      // 子组件已经处理了路由跳转和消息提示
      // 这里可以添加额外的逻辑，比如统计等
      console.log('菜单选择:', item)
    }

    return {
      layoutStore,
      notificationCount,
      currentRoute,
      displayUsername,
      breadcrumbList,
      toggleFullscreen,
      goToProfile,
      handleLogout,
      // 菜单搜索弹窗相关
      menuSearchDialogVisible,
      openMenuSearchDialog,
      handleMenuSelect
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 24px;
  background: var(--bg-color-header);
  position: relative;
}

// 底部光线效果 - 仅在深色模式下显示
[data-theme="dark"] .admin-header {
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg,
      rgba(59, 130, 246, 0.1),
      rgba(59, 130, 246, 0.4),
      var(--primary-color),
      rgba(59, 130, 246, 0.4),
      rgba(59, 130, 246, 0.1)
    );
    opacity: 0.8;
    box-shadow:
      0 0 8px rgba(59, 130, 246, 0.4),
      0 0 16px rgba(59, 130, 246, 0.2);
    animation: headerGlow 3s ease-in-out infinite alternate;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0; // 防止flex子元素溢出
}

.sidebar-toggle {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-lg);
  color: var(--text-color-regular);
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  

  // 内部光效
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    border-radius: var(--border-radius-lg);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // 悬停时的光效扫过动画
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
  }

  &:hover {
    transform: scale(1.05);
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.3) inset;
    animation: pulse 2s infinite;

    &::before {
      opacity: 1;
    }

    &::after {
      left: 100%;
    }

    .el-icon {
      transform: scale(1.1);
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    }
  }

  &:active {
    transform: scale(0.95);
    box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.3),
      0 0 15px rgba(59, 130, 246, 0.2) inset;
  }

  .el-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.breadcrumb {
  display: flex;
  font-size: 14px;

  .breadcrumb-icon {
    margin-right: 4px;
    font-size: 12px;
  }

  :deep(.el-breadcrumb__item) {
    .el-breadcrumb__inner {
      color: var(--text-color-secondary);
      display: flex;
      align-items: center;

      &:hover {
        color: var(--primary-color);
      }
    }
    
    &:last-child .el-breadcrumb__inner {
      color: var(--text-color-primary);
      font-weight: 500;
    }
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-shrink: 0; // 防止右侧按钮被压缩
}





.header-btn {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-lg);
  color: var(--text-color-regular);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  // 默认状态的微妙背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-lg);
    opacity: 0;
    transition: all 0.3s ease;
  }

  &:hover {
    color: var(--text-color-white);
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.8),
      rgba(37, 99, 235, 0.9)
    );
    transform: translateY(-2px);
    box-shadow:
      0 4px 12px rgba(59, 130, 246, 0.3),
      0 0 20px rgba(59, 130, 246, 0.1) inset;

    &::before {
      opacity: 1;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
    }

    .el-icon {
      transform: scale(1.1);
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow:
      0 2px 8px rgba(59, 130, 246, 0.2),
      0 0 15px rgba(59, 130, 246, 0.1) inset;
  }
}

// 主题切换组件在Header中的样式
:deep(.theme-toggle) {
  .theme-toggle-button {
    width: 40px !important;
    height: 40px !important;
    border-radius: var(--border-radius-lg) !important;
    background: transparent !important;
    border: none !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--text-color-regular) !important;
    position: relative;
    overflow: hidden;

    // 默认状态的微妙背景
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--border-radius-lg);
      opacity: 0;
      transition: all 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.8),
        rgba(37, 99, 235, 0.9)
      ) !important;
      transform: translateY(-2px);
      box-shadow:
        0 4px 12px rgba(59, 130, 246, 0.3),
        0 0 20px rgba(59, 130, 246, 0.1) inset;

      &::before {
        opacity: 1;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
      }

      .theme-icon {
        color: var(--text-color-white) !important;
        transform: scale(1.1);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }
    }

    &:active {
      transform: translateY(0);
      box-shadow:
        0 2px 8px rgba(59, 130, 246, 0.2),
        0 0 15px rgba(59, 130, 246, 0.1) inset;
    }

    &:focus {
      outline: none;
      box-shadow: var(--box-shadow-focus);
    }
  }

  .icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
  }

  .theme-icon {
    transition: all 0.3s ease;
    width: 18px !important;
    height: 18px !important;
  }
}

.notification-dropdown {
  .el-badge {
    :deep(.el-badge__content) {
      background: var(--danger-color);
      border: none;
      transition: all 0.3s ease;
    }

    // 通知按钮特殊悬停效果
    .header-btn {
      &:hover {
        background: linear-gradient(135deg,
          rgba(239, 68, 68, 0.8),
          rgba(220, 38, 38, 0.9)
        ) !important;

        .el-icon {
          color: var(--text-color-white) !important;
        }
      }
    }

    // 悬停时徽章效果
    &:hover {
      :deep(.el-badge__content) {
        background: var(--text-color-white);
        color: var(--danger-color);
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
      }
    }
  }
}

.notification-item {
  padding: 8px 0;
  min-width: 240px;
  
  .notification-title {
    font-weight: 500;
    color: var(--text-color-primary);
    margin-bottom: 4px;
  }
  
  .notification-content {
    font-size: 12px;
    color: var(--text-color-secondary);
    margin-bottom: 4px;
  }
  
  .notification-time {
    font-size: 11px;
    color: var(--text-color-placeholder);
  }
}

.user-dropdown {
  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--bg-color-page);
    }
    
    .username {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color-primary);
    }
    
    .dropdown-icon {
      color: var(--text-color-secondary);
      transition: transform 0.3s ease;
    }
    
    &:hover .dropdown-icon {
      transform: rotate(180deg);
    }
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  
  &:hover {
    background: var(--bg-color-page);
    color: var(--primary-color);
  }
}

// 脉冲动画效果
@keyframes pulse {
  0% {
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.3) inset,
      0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.3) inset,
      0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow:
      0 8px 25px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.3) inset,
      0 0 0 0 rgba(59, 130, 246, 0);
  }
}

// Header底部光线动画
@keyframes headerGlow {
  0% {
    opacity: 0.6;
    box-shadow:
      0 0 8px rgba(59, 130, 246, 0.3),
      0 0 16px rgba(59, 130, 246, 0.1);
  }
  50% {
    opacity: 1;
    box-shadow:
      0 0 12px rgba(59, 130, 246, 0.5),
      0 0 24px rgba(59, 130, 246, 0.3);
  }
  100% {
    opacity: 0.8;
    box-shadow:
      0 0 10px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(59, 130, 246, 0.2);
  }
}

// 响应式设计
// 移动端 (< 768px)
@media (max-width: 767px) {
  .admin-header {
    padding: 0 12px;
  }

  .header-left {
    gap: 8px;

    .breadcrumb {
      display: none; // 移动端隐藏面包屑
    }
  }

  .header-right {
    gap: 8px;

    // 移动端隐藏部分功能按钮
    .notification-dropdown,
    .header-btn:not(.sidebar-toggle) {
      display: none;
    }

    // 只保留用户信息和主题切换
    .user-dropdown,
    :deep(.theme-toggle) {
      display: flex;
    }

    .user-info {
      .username {
        display: none; // 移动端隐藏用户名
      }
    }
  }
}

// 平板端 (768px - 1023px)
@media (min-width: 768px) and (max-width: 1023px) {
  .admin-header {
    padding: 0 16px;
  }

  .header-left {
    gap: 12px;
  }

  .header-right {
    gap: 12px;

    .user-info {
      .username {
        display: none; // 平板端也隐藏用户名
      }
    }
  }

  .breadcrumb {
    // 平板端面包屑样式调整
    :deep(.el-breadcrumb__item) {
      .el-breadcrumb__inner {
        font-size: 13px;
      }
    }
  }
}

// 桌面端 (>= 1024px)
@media (min-width: 1024px) {
  .admin-header {
    padding: 0 24px;
  }

  .header-left {
    gap: 16px;
  }

  .header-right {
    gap: 16px;
  }
}

// 移动端横屏适配
@media (max-width: 1024px) and (orientation: landscape) {
  .admin-header {
    height: 50px; // 横屏时减少头部高度
    padding: 0 16px;
    min-height: 50px;
  }

  .header-left {
    gap: 8px;
  }

  .header-right {
    gap: 8px;

    .user-info {
      .username {
        display: none; // 横屏时隐藏用户名节省空间
      }
    }
  }

  .sidebar-toggle {
    width: 36px;
    height: 36px;
  }

  .breadcrumb {
    // 横屏时面包屑样式调整
    :deep(.el-breadcrumb__item) {
      .el-breadcrumb__inner {
        font-size: 12px;
      }
    }
  }

  // 横屏时工具按钮尺寸调整
  .tool-button {
    width: 36px;
    height: 36px;
  }
}
</style>
