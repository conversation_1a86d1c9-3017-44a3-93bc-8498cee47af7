<template>
  <div class="tags-view-container">
    <div
      class="tags-view-wrapper"
      ref="scrollContainer"
    >
      <div class="tags-view-content" ref="scrollContent">
        <div
          v-for="tag in visitedViews"
          :key="tag.path"
          :class="[
            'tags-view-item',
            {
              'active': isActive(tag),
              'selected': selectedTag?.path === tag.path
            }
          ]"
          @click="handleTagClick(tag)"
          @contextmenu.prevent="handleRightClick(tag, $event)"
          @mouseenter="handleTagHover(tag)"
          @mouseleave="handleTagLeave"
        >
          <!-- 图标 -->
          <svg-icon
            v-if="tag.meta?.icon"
            :name="tag.meta.icon"
            :color="isActive(tag) ? '#ffffff' : 'currentColor'"
            class="tag-icon"
          />

          <!-- 标题 -->
          <span class="tag-title">{{ tag.title }}</span>

          <!-- 关闭按钮 - 只在非固定标签时显示，通过CSS控制透明度 -->
          <el-icon
            v-if="!tag.meta?.affix"
            :class="[
              'tag-close',
              {
                'visible': isActive(tag) || hoveredTag?.path === tag.path
              }
            ]"
            @click.stop="handleCloseTag(tag)"
          >
            <Close />
          </el-icon>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-show="contextMenuVisible"
      :style="{ left: contextMenuLeft + 'px', top: contextMenuTop + 'px' }"
      class="context-menu"
      ref="contextMenu"
    >
      <div class="context-menu-item" @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        <span>刷新页面</span>
      </div>
      <div
        v-if="!selectedTag?.meta?.affix"
        class="context-menu-item"
        @click="handleCloseTag(selectedTag)"
      >
        <el-icon><Close /></el-icon>
        <span>关闭标签</span>
      </div>
      <div class="context-menu-item" @click="handleCloseOthers">
        <el-icon><Remove /></el-icon>
        <span>关闭其他</span>
      </div>
      <div class="context-menu-item" @click="handleCloseAll">
        <el-icon><CircleClose /></el-icon>
        <span>关闭所有</span>
      </div>
    </div>

    <!-- 遮罩层，用于关闭右键菜单 -->
    <div
      v-show="contextMenuVisible"
      class="context-menu-overlay"
      @click="closeContextMenu"
    ></div>
  </div>
</template>

<script>
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTagsViewStore } from '@/stores/tagsView'
import { Close, Refresh, Remove, CircleClose } from '@element-plus/icons-vue'
import { createTouchEventOptimizer } from '@/utils/eventOptimizer'

export default {
  name: 'TagsView',
  components: {
    Close,
    Refresh,
    Remove,
    CircleClose
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const tagsViewStore = useTagsViewStore()

    // 模板引用
    const scrollContainer = ref(null)
    const scrollContent = ref(null)
    const contextMenu = ref(null)

    // 右键菜单状态
    const contextMenuVisible = ref(false)
    const contextMenuLeft = ref(0)
    const contextMenuTop = ref(0)
    const selectedTag = ref(null)

    // 悬停状态
    const hoveredTag = ref(null)

    // 触摸拖拽状态
    const touchState = ref({
      startX: 0,
      startScrollLeft: 0,
      isDragging: false
    })

    // 触摸事件优化器
    let touchOptimizer = null

    // 计算属性
    const visitedViews = computed(() => tagsViewStore.visitedViews)

    /**
     * 判断标签是否为当前激活状态
     * @param {Object} tag 标签对象
     * @returns {boolean}
     */
    const isActive = (tag) => {
      return tag.path === route.path
    }

    /**
     * 处理标签点击
     * @param {Object} tag 标签对象
     */
    const handleTagClick = (tag) => {
      if (tag.path !== route.path) {
        router.push({
          path: tag.path,
          query: tag.query,
          params: tag.params
        })
      }
    }

    /**
     * 处理关闭标签
     * @param {Object} tag 要关闭的标签
     */
    const handleCloseTag = async (tag) => {
      if (tag.meta?.affix) return // 固定标签不能关闭

      const isCurrentTag = tag.path === route.path
      
      await tagsViewStore.delView(tag)
      
      // 如果关闭的是当前标签，需要跳转到其他标签
      if (isCurrentTag) {
        const remainingViews = tagsViewStore.visitedViews
        if (remainingViews.length > 0) {
          // 跳转到最后一个标签
          const lastView = remainingViews[remainingViews.length - 1]
          router.push({
            path: lastView.path,
            query: lastView.query,
            params: lastView.params
          })
        } else {
          // 如果没有其他标签，跳转到首页
          router.push('/dashboard')
        }
      }
      
      closeContextMenu()
    }

    /**
     * 处理标签悬停
     * @param {Object} tag 标签对象
     */
    const handleTagHover = (tag) => {
      hoveredTag.value = tag
    }

    /**
     * 处理标签离开悬停
     */
    const handleTagLeave = () => {
      hoveredTag.value = null
    }

    /**
     * 处理触摸开始
     * @param {TouchEvent} event 触摸事件
     */
    const handleTouchStart = (event) => {
      if (event.touches.length === 1) {
        touchState.value.startX = event.touches[0].clientX
        touchState.value.startScrollLeft = scrollContainer.value.scrollLeft
        touchState.value.isDragging = false
      }
    }

    /**
     * 处理触摸移动
     * @param {TouchEvent} event 触摸事件
     */
    const handleTouchMove = (event) => {
      if (event.touches.length === 1 && scrollContainer.value) {
        event.preventDefault()
        const currentX = event.touches[0].clientX
        const deltaX = touchState.value.startX - currentX

        if (Math.abs(deltaX) > 5) {
          touchState.value.isDragging = true
        }

        scrollContainer.value.scrollLeft = touchState.value.startScrollLeft + deltaX
      }
    }

    /**
     * 处理触摸结束
     */
    const handleTouchEnd = () => {
      touchState.value.isDragging = false
    }

    /**
     * 处理右键点击
     * @param {Object} tag 标签对象
     * @param {Event} event 事件对象
     */
    const handleRightClick = (tag, event) => {
      selectedTag.value = tag
      contextMenuLeft.value = event.clientX
      contextMenuTop.value = event.clientY
      contextMenuVisible.value = true

      // 确保菜单不会超出屏幕
      nextTick(() => {
        if (contextMenu.value) {
          const menuRect = contextMenu.value.getBoundingClientRect()
          const windowWidth = window.innerWidth
          const windowHeight = window.innerHeight

          if (contextMenuLeft.value + menuRect.width > windowWidth) {
            contextMenuLeft.value = windowWidth - menuRect.width - 10
          }
          if (contextMenuTop.value + menuRect.height > windowHeight) {
            contextMenuTop.value = windowHeight - menuRect.height - 10
          }
        }
      })
    }

    /**
     * 关闭右键菜单
     */
    const closeContextMenu = () => {
      contextMenuVisible.value = false
      selectedTag.value = null
    }

    /**
     * 刷新页面
     */
    const handleRefresh = () => {
      // 先从缓存中移除当前组件
      if (selectedTag.value?.name) {
        tagsViewStore.delCachedView(selectedTag.value)
      }
      
      // 重新加载当前路由
      router.replace({
        path: '/redirect' + selectedTag.value.path,
        query: selectedTag.value.query
      })
      
      closeContextMenu()
    }

    /**
     * 关闭其他标签
     */
    const handleCloseOthers = async () => {
      await tagsViewStore.delOthersViews(selectedTag.value)
      
      // 如果当前页面不是选中的标签，需要跳转
      if (selectedTag.value.path !== route.path) {
        router.push({
          path: selectedTag.value.path,
          query: selectedTag.value.query,
          params: selectedTag.value.params
        })
      }
      
      closeContextMenu()
    }

    /**
     * 关闭所有标签
     */
    const handleCloseAll = async () => {
      await tagsViewStore.delAllViews()
      
      // 跳转到首页
      router.push('/dashboard')
      
      closeContextMenu()
    }

    /**
     * 滚动到当前激活的标签
     */
    const scrollToActiveTag = () => {
      nextTick(() => {
        const activeTag = document.querySelector('.tags-view-item.active')
        if (activeTag && scrollContainer.value) {
          const containerRect = scrollContainer.value.getBoundingClientRect()
          const tagRect = activeTag.getBoundingClientRect()
          
          if (tagRect.left < containerRect.left || tagRect.right > containerRect.right) {
            const scrollLeft = activeTag.offsetLeft - (containerRect.width - tagRect.width) / 2
            scrollContainer.value.scrollTo({
              left: scrollLeft,
              behavior: 'smooth'
            })
          }
        }
      })
    }

    // 监听点击事件，关闭右键菜单
    const handleDocumentClick = (event) => {
      if (contextMenuVisible.value && contextMenu.value && !contextMenu.value.contains(event.target)) {
        closeContextMenu()
      }
    }

    // 生命周期
    onMounted(() => {
      document.addEventListener('click', handleDocumentClick)

      // 初始化触摸事件优化器
      if (scrollContainer.value) {
        touchOptimizer = createTouchEventOptimizer(scrollContainer.value)
        touchOptimizer.addTouchStart(handleTouchStart)
        touchOptimizer.addTouchMove(handleTouchMove, true) // 需要阻止默认行为
        touchOptimizer.addTouchEnd(handleTouchEnd)
      }

      scrollToActiveTag()
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleDocumentClick)

      // 销毁触摸事件优化器
      if (touchOptimizer) {
        touchOptimizer.destroy()
        touchOptimizer = null
      }
    })

    return {
      // 模板引用
      scrollContainer,
      scrollContent,
      contextMenu,

      // 状态
      visitedViews,
      contextMenuVisible,
      contextMenuLeft,
      contextMenuTop,
      selectedTag,
      hoveredTag,

      // 方法
      isActive,
      handleTagClick,
      handleCloseTag,
      handleTagHover,
      handleTagLeave,
      handleTouchStart,
      handleTouchMove,
      handleTouchEnd,
      handleRightClick,
      closeContextMenu,
      handleRefresh,
      handleCloseOthers,
      handleCloseAll
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 40px;
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color-lighter);
  position: relative;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tags-view-wrapper {
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;

  // 隐藏滚动条但保持滚动功能
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  // 移动端触摸滚动优化
  -webkit-overflow-scrolling: touch;
}

.tags-view-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
  gap: 6px;
  min-width: max-content;
}

.tags-view-item {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 12px;
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  user-select: none;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color-regular);
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);

  // 添加微妙的渐变效果
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(var(--primary-color-rgb), 0.05) 100%);
    opacity: 0;
    transition: opacity 0.25s ease;
  }

  &:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: var(--primary-light);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.15);

    .tag-icon {
      color: var(--primary-color);
    }

    &::before {
      opacity: 1;
    }
  }

  &.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 3px 12px rgba(var(--primary-color-rgb), 0.3);

    &::before {
      opacity: 0;
    }

    .tag-icon {
      color: #ffffff !important; // 强制与标题颜色保持一致
    }

    .tag-close {
      color: rgba(255, 255, 255, 0.9);

      &.visible {
        opacity: 0.8;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        color: #ffffff !important;
        opacity: 1 !important;
      }
    }
  }

  &.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px rgba(var(--primary-color-rgb), 0.2);
  }
}

.tag-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  color: var(--text-color-regular);
  transition: color 0.25s ease;
}

.tag-title {
  margin-right: 8px;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.tag-close {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  margin-left: 2px;
  transform: scale(0.8);

  &.visible {
    opacity: 0.7;
    transform: scale(1);
  }

  &:hover {
    background: rgba(0, 0, 0, 0.1);
    opacity: 1 !important;
    transform: scale(1.1) !important;
  }
}

// 右键菜单
.context-menu {
  position: fixed;
  background: var(--bg-color);
  border: 1px solid var(--border-color-lighter);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(8px);
  z-index: 1000;
  min-width: 140px;
  padding: 6px 0;
  overflow: hidden;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-color-regular);
  transition: all 0.2s ease;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform 0.2s ease;
  }

  &:hover {
    background: var(--bg-color-hover);
    color: var(--primary-color);
    padding-left: 20px;

    &::before {
      transform: scaleY(1);
    }
  }

  .el-icon {
    margin-right: 10px;
    font-size: 14px;
    transition: transform 0.2s ease;
  }

  &:hover .el-icon {
    transform: scale(1.1);
  }
}

.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: rgba(0, 0, 0, 0.01);
}

// 深色主题适配
[data-theme="dark"] {
  .tags-view-container {
    background: var(--bg-color-page);
    border-bottom-color: var(--border-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .tags-view-item {
    background: var(--bg-color-card); // 使用卡片背景色，与主题保持一致
    border-color: var(--border-color);
    color: var(--text-color-regular);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

    .tag-icon {
      color: var(--text-color-regular);
    }

    &:hover {
      background: var(--bg-color-hover);
      border-color: var(--primary-light);
      box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.25);

      .tag-icon {
        color: var(--primary-color);
      }
    }

    &.active {
      // 深色模式下保持与浅色主题一致的选中效果
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
      border-color: var(--primary-color) !important;
      color: #ffffff !important;
      box-shadow: 0 3px 12px rgba(var(--primary-color-rgb), 0.4);

      .tag-icon {
        color: #ffffff !important; // 强制与标题颜色保持一致
      }
    }
  }

  .context-menu {
    background: var(--bg-color-card);
    border-color: var(--border-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(12px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tags-view-container {
    height: 36px;
  }

  .tags-view-content {
    padding: 0 12px;
    gap: 4px;
  }

  .tags-view-item {
    height: 26px;
    padding: 0 10px;
    font-size: 12px;
    border-radius: 4px;

    &:hover {
      transform: none; // 移动端不使用悬浮效果
    }

    &.active {
      transform: none;
    }
  }

  .tag-icon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
  }

  .tag-title {
    margin-right: 6px;
  }

  .tag-close {
    width: 16px;
    height: 16px;
    margin-left: 0;

    &:hover {
      transform: none;
    }
  }

  .context-menu {
    min-width: 120px;
  }

  .context-menu-item {
    padding: 8px 12px;
    font-size: 12px;

    &:hover {
      padding-left: 16px;
    }
  }
}

// 平板适配
@media (min-width: 769px) and (max-width: 1024px) {
  .tags-view-item {
    height: 28px;
    font-size: 12px;
  }
}
</style>
