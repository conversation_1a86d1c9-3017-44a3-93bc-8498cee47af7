/**
 * 用户数据清理工具
 * 提供统一的用户数据清理功能，避免循环依赖
 */

/**
 * 清理localStorage中的用户相关数据
 * 这是一个纯函数，不依赖任何store，避免循环依赖
 */
export const clearUserDataFromStorage = () => {
  try {
    // 清理认证相关
    localStorage.removeItem('token')
    localStorage.removeItem('hasLoginHistory')
    localStorage.removeItem('redirectPath')

    // 清理谷歌验证码设置提示相关缓存
    localStorage.removeItem('google_auth_setup_choice')
    localStorage.removeItem('google_auth_next_reminder')

    // 注意：保留记住我相关的加密数据（用户名、密码、记住我状态等）


  } catch (error) {
    console.error('清理localStorage数据失败:', error)
  }
}

/**
 * 获取当前页面路径并保存为重定向路径
 * @param {Array<string>} excludePaths 需要排除的路径列表
 */
export const saveCurrentPathForRedirect = (excludePaths = ['/login', '/session-expired']) => {
  try {
    const currentPath = window.location.pathname + window.location.search
    const shouldSave = !excludePaths.some(path => currentPath.startsWith(path))
    
    if (shouldSave) {
      localStorage.setItem('redirectPath', currentPath)
    }
  } catch (error) {
    console.error('保存重定向路径失败:', error)
  }
}

/**
 * 检查是否有登录历史
 * @returns {boolean}
 */
export const hasLoginHistory = () => {
  return localStorage.getItem('hasLoginHistory') === 'true'
}

/**
 * 处理401未授权错误的通用逻辑
 * 这个函数可以在request拦截器中使用，避免循环依赖
 */
export const handle401Error = () => {
  // 检查登录历史
  const hadLoginHistory = hasLoginHistory()
  
  // 保存当前路径用于重定向
  saveCurrentPathForRedirect()
  
  // 清理所有用户数据
  clearUserDataFromStorage()
  
  // 根据登录历史决定跳转页面
  if (hadLoginHistory) {
    window.location.href = '/session-expired?reason=expired'
    return '登录已失效，请重新登录'
  } else {
    window.location.href = '/session-expired?reason=unauthorized'
    return '需要登录后才能访问，请先登录'
  }
}
