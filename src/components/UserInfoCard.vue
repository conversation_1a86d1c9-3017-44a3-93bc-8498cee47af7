<template>
  <el-card class="user-info-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">用户信息</span>
        <el-button 
          type="primary" 
          size="small" 
          :loading="loading"
          @click="refreshUserInfo"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>

    <div v-if="loading && !userInfo" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>

    <div v-else-if="userInfo" class="user-info-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">用户ID:</span>
            <span class="value">{{ userInfo.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户名:</span>
            <span class="value">{{ userInfo.username }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户类型:</span>
            <el-tag :type="userInfo.isAdmin ? 'danger' : 'primary'">
              {{ userInfo.userTypeName }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">账户状态:</span>
            <el-tag :type="userInfo.accountLocked === 0 ? 'success' : 'danger'">
              {{ userInfo.accountLocked === 0 ? '正常' : '已锁定' }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 安全信息 -->
      <div class="info-section">
        <h4 class="section-title">安全信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">是否管理员:</span>
            <el-tag :type="userInfo.isAdmin ? 'danger' : 'info'">
              {{ userInfo.isAdmin ? '是' : '否' }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">谷歌验证:</span>
            <el-tag :type="userInfo.hasGoogleAuth ? 'success' : 'warning'">
              {{ userInfo.hasGoogleAuth ? '已设置' : '未设置' }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">密码更新时间:</span>
            <span class="value">{{ formatDateTime(userInfo.passwordUpdateTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 登录信息 -->
      <div class="info-section">
        <h4 class="section-title">登录信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">最后登录时间:</span>
            <span class="value">{{ formatDateTime(userInfo.lastLoginTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">最后登录IP:</span>
            <span class="value">{{ userInfo.lastLoginIp || '未知' }}</span>
          </div>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="info-section">
        <h4 class="section-title">系统信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDateTime(userInfo.createTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">更新时间:</span>
            <span class="value">{{ formatDateTime(userInfo.updateTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">版本号:</span>
            <span class="value">{{ userInfo.version }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-empty description="暂无用户信息" />
    </div>
  </el-card>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'

// 使用auth store
const authStore = useAuthStore()

// 计算属性
const userInfo = computed(() => authStore.userInfo)
const loading = computed(() => authStore.loading)

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    await authStore.fetchUserInfo()
    ElMessage.success('用户信息刷新成功')
  } catch (error) {
    ElMessage.error('刷新用户信息失败')
  }
}

// 组件挂载时获取用户信息
onMounted(async () => {
  if (!userInfo.value) {
    try {
      await authStore.fetchUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})
</script>

<style scoped>
.user-info-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.loading-container {
  padding: 20px 0;
}

.user-info-content {
  padding: 10px 0;
}

.info-section {
  margin-bottom: 24px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 120px;
  flex-shrink: 0;
}

.value {
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.empty-state {
  padding: 40px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 6px 0;
  }
  
  .label {
    min-width: auto;
    margin-bottom: 4px;
    font-size: 13px;
  }
  
  .value {
    font-size: 14px;
  }
}
</style>
