import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { menuApi } from '@/api'
import { triggerRouteRefresh } from '@/utils/routeRefresh'

/**
 * 菜单管理状态管理
 * 专门用于菜单管理页面的状态和数据管理
 */
export const useMenuManagementStore = defineStore('menuManagement', () => {
  // 状态
  const menuList = ref([])
  const loading = ref(false)
  const submitLoading = ref(false)
  const selectedMenus = ref([])
  const isExpandAll = ref(false)
  
  // 搜索条件
  const searchForm = reactive({
    menuName: '',
    menuType: null,
    status: null
  })
  
  // 菜单表单数据
  const menuForm = reactive({
    id: '',
    parentId: null,
    menuName: '',
    menuType: 2,
    menuCode: '',
    routePath: '',
    componentPath: '',
    icon: '',
    sortOrder: 0,
    visible: 1,
    status: 1,
    permission: '',
    externalLink: 0,
    openMode: '_self',
    routeParams: ''
  })
  
  // 弹窗状态
  const dialogVisible = ref(false)
  const dialogTitle = ref('')
  const isEdit = ref(false)

  /**
   * 将扁平的菜单数据转换为树形结构
   * @param {Array} flatData 扁平数据
   * @returns {Array} 树形数据
   */
  const buildMenuTree = (flatData) => {
    if (!flatData || !Array.isArray(flatData)) {
      return []
    }

    // 创建一个映射表，以便快速查找
    const menuMap = new Map()
    const result = []

    // 首先将所有菜单项放入映射表，并初始化children数组和hasChildren属性
    flatData.forEach(menu => {
      menuMap.set(menu.id, { ...menu, children: [], hasChildren: false })
    })

    // 构建树形结构
    flatData.forEach(menu => {
      const menuItem = menuMap.get(menu.id)

      if (menu.parentId && menuMap.has(menu.parentId)) {
        // 有父菜单，添加到父菜单的children中
        const parent = menuMap.get(menu.parentId)
        parent.children.push(menuItem)
        parent.hasChildren = true // 标记父节点有子节点
      } else {
        // 没有父菜单或父菜单不存在，作为根节点
        result.push(menuItem)
      }
    })

    // 递归排序（按sortOrder排序）
    const sortMenus = (menus) => {
      menus.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
      menus.forEach(menu => {
        if (menu.children && menu.children.length > 0) {
          menu.hasChildren = true
          sortMenus(menu.children)
        } else {
          menu.hasChildren = false
        }
      })
    }

    sortMenus(result)
    return result
  }

  /**
   * 获取菜单列表
   * @param {Object} params 查询参数
   * @returns {Promise<Array>}
   */
  const fetchMenuList = async (params = {}) => {
    try {
      loading.value = true
      const queryParams = { ...searchForm, ...params }
      const response = await menuApi.getMenus(queryParams)

      // 将扁平数据转换为树形结构
      const treeData = buildMenuTree(response || [])
      menuList.value = treeData

      console.log('菜单数据转换完成:', {
        原始数据长度: response?.length || 0,
        原始数据: response,
        树形数据长度: treeData.length,
        树形数据: treeData
      })

      return menuList.value
    } catch (error) {
      console.error('获取菜单列表失败:', error)
      ElMessage.error('获取菜单列表失败')
      menuList.value = []
      throw error
    } finally {
      loading.value = false
    }
  }

  /**
   * 根据ID获取菜单详情
   * @param {string} id 菜单ID
   * @returns {Promise<Object>}
   */
  const fetchMenuById = async (id) => {
    try {
      const response = await menuApi.getMenuById(id)
      return response
    } catch (error) {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('获取菜单详情失败:', error)
      throw error
    }
  }

  /**
   * 创建菜单
   * @param {Object} data 菜单数据
   * @returns {Promise<Object>}
   */
  const createMenu = async (data) => {
    try {
      submitLoading.value = true
      const response = await menuApi.createMenu(data)
      ElMessage.success('创建菜单成功')
      await fetchMenuList() // 刷新列表
      triggerRouteRefresh() // 触发路由刷新
      return response
    } catch (error) {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('创建菜单失败:', error)
      throw error
    } finally {
      submitLoading.value = false
    }
  }

  /**
   * 更新菜单
   * @param {Object} data 菜单数据
   * @returns {Promise<Object>}
   */
  const updateMenu = async (data) => {
    try {
      submitLoading.value = true
      const response = await menuApi.updateMenu(data)
      ElMessage.success('更新菜单成功')
      await fetchMenuList() // 刷新列表
      triggerRouteRefresh() // 触发路由刷新
      return response
    } catch (error) {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('更新菜单失败:', error)
      throw error
    } finally {
      submitLoading.value = false
    }
  }

  /**
   * 删除菜单
   * @param {string} id 菜单ID
   * @returns {Promise<Object>}
   */
  const deleteMenu = async (id) => {
    try {
      const response = await menuApi.deleteMenu(id)
      ElMessage.success('删除菜单成功')
      await fetchMenuList() // 刷新列表
      triggerRouteRefresh() // 触发路由刷新
      return response
    } catch (error) {
      console.error('删除菜单失败:', error)
      throw error
    }
  }

  /**
   * 批量更新菜单状态
   * @param {Array<string>} menuIds 菜单ID列表
   * @param {number} status 状态：0-禁用，1-启用
   * @returns {Promise<Object>}
   */
  const batchUpdateStatus = async (menuIds, status) => {
    try {
      const response = await menuApi.batchUpdateStatus({
        menuIds,
        status
      })
      ElMessage.success(`批量${status === 1 ? '启用' : '禁用'}成功`)
      await fetchMenuList() // 刷新列表
      triggerRouteRefresh() // 触发路由刷新
      return response
    } catch (error) {
      console.error('批量更新状态失败:', error)
      ElMessage.error('批量更新状态失败')
      throw error
    }
  }

  /**
   * 更新单个菜单状态
   * @param {string} id 菜单ID
   * @param {number} status 状态：0-禁用，1-启用
   * @returns {Promise<Object>}
   */
  const updateMenuStatus = async (id, status) => {
    try {
      const response = await menuApi.batchUpdateStatus({
        menuIds: [id],
        status
      })
      ElMessage.success('状态更新成功')
      triggerRouteRefresh() // 触发路由刷新
      return response
    } catch (error) {
      console.error('状态更新失败:', error)
      ElMessage.error('状态更新失败')
      throw error
    }
  }

  /**
   * 设置搜索条件
   * @param {Object} params 搜索参数
   */
  const setSearchForm = (params) => {
    Object.assign(searchForm, params)
  }

  /**
   * 重置搜索条件
   */
  const resetSearchForm = () => {
    Object.assign(searchForm, {
      menuName: '',
      menuType: null,
      status: null
    })
  }

  /**
   * 设置菜单表单数据
   * @param {Object} data 表单数据
   */
  const setMenuForm = (data) => {
    Object.assign(menuForm, data)
  }

  /**
   * 重置菜单表单
   */
  const resetMenuForm = () => {
    Object.assign(menuForm, {
      id: '',
      parentId: null,
      menuName: '',
      menuType: 2,
      menuCode: '',
      routePath: '',
      componentPath: '',
      icon: '',
      sortOrder: 0,
      visible: 1,
      status: 1,
      permission: '',
      externalLink: 0,
      openMode: '_self',
      routeParams: ''
    })
  }

  /**
   * 设置选中的菜单
   * @param {Array} menus 选中的菜单列表
   */
  const setSelectedMenus = (menus) => {
    selectedMenus.value = menus
  }

  /**
   * 设置展开状态
   * @param {boolean} expand 是否展开
   */
  const setExpandAll = (expand) => {
    isExpandAll.value = expand
  }

  /**
   * 打开弹窗
   * @param {string} title 弹窗标题
   * @param {boolean} edit 是否为编辑模式
   */
  const openDialog = (title, edit = false) => {
    dialogTitle.value = title
    isEdit.value = edit
    dialogVisible.value = true
  }

  /**
   * 关闭弹窗
   */
  const closeDialog = () => {
    dialogVisible.value = false
    resetMenuForm()
  }

  /**
   * 构建父级菜单选项
   * @returns {Array} 父级菜单选项
   */
  const getParentMenuOptions = () => {
    const buildOptions = (menus) => {
      return menus.filter(menu => menu.menuType !== 3).map(menu => ({
        id: menu.id,
        menuName: menu.menuName,
        children: menu.children ? buildOptions(menu.children) : []
      }))
    }
    return buildOptions(menuList.value)
  }



  /**
   * 清除所有数据
   */
  const clearAll = () => {
    menuList.value = []
    selectedMenus.value = []
    resetSearchForm()
    resetMenuForm()
    closeDialog()
  }

  return {
    // 状态
    menuList,
    loading,
    submitLoading,
    selectedMenus,
    isExpandAll,
    searchForm,
    menuForm,
    dialogVisible,
    dialogTitle,
    isEdit,

    // 方法
    fetchMenuList,
    fetchMenuById,
    createMenu,
    updateMenu,
    deleteMenu,
    batchUpdateStatus,
    updateMenuStatus,
    setSearchForm,
    resetSearchForm,
    setMenuForm,
    resetMenuForm,
    setSelectedMenus,
    setExpandAll,
    openDialog,
    closeDialog,
    getParentMenuOptions,
    clearAll
  }
})
