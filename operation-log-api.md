# 操作日志API文档

## 概述

操作日志模块提供了完整的用户操作行为记录、查询、统计和分析功能。支持系统审计、操作追踪和行为分析。

## 基础信息

- **基础路径**: `/system/operation-log`
- **权限前缀**: `operation:log`
- **支持格式**: JSON
- **时间格式**: `yyyy-MM-dd HH:mm:ss`
- **权限模式**: 支持权限验证或超级管理员角色

## API接口列表

### 1. 分页查询操作日志

**接口地址**: `GET /system/operation-log/page`

**权限要求**: `operation:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| userId | String | 否 | - | 用户ID |
| username | String | 否 | - | 用户名（模糊查询） |
| operationModule | String | 否 | - | 操作模块 |
| operationType | String | 否 | - | 操作类型 |
| operationStatus | Integer | 否 | - | 操作状态(0-失败,1-成功) |
| requestMethod | String | 否 | - | 请求方法(GET/POST/PUT/DELETE等) |
| clientIp | String | 否 | - | 客户端IP地址 |
| startTime | DateTime | 否 | - | 开始时间 |
| endTime | DateTime | 否 | - | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": "1234567890",
        "userId": "user123",
        "username": "admin",
        "operationTime": "2025-07-30 10:30:00",
        "operationModule": "用户管理",
        "operationType": "CREATE",
        "operationDesc": "创建用户",
        "operationStatus": 1,
        "requestMethod": "POST",
        "requestUrl": "/api/user/create",
        "clientIp": "*************",
        "executionTime": 150
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  },
  "success": true
}
```

### 2. 获取操作日志详情

**接口地址**: `GET /system/operation-log/{id}`

**权限要求**: `operation:log:view` 或超级管理员角色

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | String | 是 | 操作日志ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": "1234567890",
    "userId": "user123",
    "username": "admin",
    "operationTime": "2025-07-30 10:30:00",
    "operationModule": "用户管理",
    "operationType": "CREATE",
    "operationDesc": "创建用户",
    "operationStatus": 1,
    "requestMethod": "POST",
    "requestUrl": "/api/user/create",
    "requestParams": {"name": "张三", "email": "<EMAIL>"},
    "responseResult": {"code": 200, "message": "创建成功"},
    "responseStatus": 200,
    "responseMessage": "创建成功",
    "clientIp": "*************",
    "realIp": "*************",
    "userAgent": "Mozilla/5.0...",
    "executionTime": 150,
    "location": "中国 北京 北京",
    "createTime": "2025-07-30 10:30:00"
  },
  "success": true
}
```

### 3. 获取用户操作历史记录

**接口地址**: `GET /system/operation-log/history/{userId}`

**权限要求**: `operation:log:view` 或超级管理员角色

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | String | 是 | 用户ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 10 | 限制数量 |

### 4. 获取操作日志统计信息

**接口地址**: `GET /system/operation-log/statistics`

**权限要求**: `operation:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| startTime | DateTime | 否 | 7天前 | 开始时间 |
| endTime | DateTime | 否 | 当前时间 | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalCount": 1000,
    "successCount": 950,
    "failureCount": 50,
    "successRate": 95.0,
    "uniqueUsers": 100,
    "uniqueIps": 200
  },
  "success": true
}
```

### 5. 按模块统计操作数量

**接口地址**: `GET /system/operation-log/statistics/module`

**权限要求**: `operation:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| startTime | DateTime | 否 | 7天前 | 开始时间 |
| endTime | DateTime | 否 | 当前时间 | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "用户管理": 500,
    "角色管理": 200,
    "菜单管理": 150,
    "系统管理": 100
  },
  "success": true
}
```

### 6. 按操作类型统计操作数量

**接口地址**: `GET /system/operation-log/statistics/type`

**权限要求**: `operation:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| startTime | DateTime | 否 | 7天前 | 开始时间 |
| endTime | DateTime | 否 | 当前时间 | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "QUERY": 600,
    "CREATE": 200,
    "UPDATE": 150,
    "DELETE": 50
  },
  "success": true
}
```

### 7. 按用户统计操作数量

**接口地址**: `GET /system/operation-log/statistics/user`

**权限要求**: `operation:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| startTime | DateTime | 否 | 7天前 | 开始时间 |
| endTime | DateTime | 否 | 当前时间 | 结束时间 |
| limit | Integer | 否 | 10 | 限制数量 |

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "admin": 500,
    "user1": 200,
    "user2": 150,
    "user3": 100
  },
  "success": true
}
```

### 8. 获取失败操作记录

**接口地址**: `GET /system/operation-log/failures`

**权限要求**: `operation:log:view` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| current | Long | 否 | 1 | 当前页码 |
| size | Long | 否 | 10 | 每页大小 |
| startTime | DateTime | 否 | 7天前 | 开始时间 |
| endTime | DateTime | 否 | 当前时间 | 结束时间 |

### 9. 根据IP地址查询操作记录

**接口地址**: `GET /system/operation-log/ip/{clientIp}`

**权限要求**: `operation:log:view` 或超级管理员角色

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| clientIp | String | 是 | 客户端IP |

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 100 | 限制数量 |

### 10. 清理过期操作日志

**接口地址**: `DELETE /system/operation-log/cleanup`

**权限要求**: `operation:log:delete` 或超级管理员角色

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| days | Integer | 否 | 30 | 清理多少天前的记录 |

**响应示例**:
```json
{
  "code": 200,
  "message": "清理成功",
  "data": 250,
  "success": true
}
```

### 11. 清空所有操作日志

**接口地址**: `DELETE /system/operation-log/clear`

**权限要求**: `operation:log:delete` 或超级管理员角色

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "message": "清空成功",
  "data": 1250,
  "success": true
}
```

**注意事项**:
- 此操作将清空所有操作日志记录
- 操作不可逆，请谨慎使用
- 建议在系统维护或重置时使用
- 操作会被记录到操作日志中

## 数据模型

### OperationLog 实体

```json
{
  "id": "操作日志ID",
  "userId": "用户ID",
  "username": "用户名",
  "operationTime": "操作时间",
  "operationModule": "操作模块",
  "operationType": "操作类型",
  "operationDesc": "操作描述",
  "operationStatus": "操作状态(0-失败,1-成功)",
  "errorMessage": "错误信息",
  "executionTime": "执行时间(毫秒)",
  "requestMethod": "请求方法",
  "requestUrl": "请求URL",
  "requestParams": "请求参数(JSON)",
  "responseResult": "响应结果(JSON)",
  "responseStatus": "响应状态码",
  "responseMessage": "响应消息",
  "clientIp": "客户端IP",
  "realIp": "真实IP",
  "userAgent": "用户代理",
  "location": "地理位置",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

### 操作类型枚举

| 类型 | 说明 | 使用场景 |
|------|------|----------|
| CREATE | 新增操作 | 创建用户、角色、菜单等 |
| UPDATE | 修改操作 | 更新用户信息、修改配置等 |
| DELETE | 删除操作 | 删除用户、角色等 |
| QUERY | 查询操作 | 查询列表、详情等 |
| LOGIN | 登录操作 | 用户登录 |
| LOGOUT | 登出操作 | 用户登出 |
| EXPORT | 导出操作 | 导出数据、报表等 |
| IMPORT | 导入操作 | 导入数据、批量操作等 |
| APPROVE | 审批操作 | 审批流程 |
| REJECT | 拒绝操作 | 拒绝审批 |
| ENABLE | 启用操作 | 启用用户、功能等 |
| DISABLE | 禁用操作 | 禁用用户、功能等 |
| RESET | 重置操作 | 重置密码、配置等 |
| OTHER | 其他操作 | 不属于以上类型的操作 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 使用示例

### 查询最近7天的操作统计
```bash
curl -X GET "http://localhost:8080/system/operation-log/statistics" \
  -H "Authorization: Bearer your-token"
```

### 查询用户操作记录
```bash
curl -X GET "http://localhost:8080/system/operation-log/page?userId=123&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 查询指定模块的操作记录
```bash
curl -X GET "http://localhost:8080/system/operation-log/page?operationModule=用户管理&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 查询失败的操作记录
```bash
curl -X GET "http://localhost:8080/system/operation-log/page?operationStatus=0&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 查询指定时间范围的操作记录
```bash
curl -X GET "http://localhost:8080/system/operation-log/page?startTime=2025-07-23 00:00:00&endTime=2025-07-30 23:59:59&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 按请求方法查询操作记录
```bash
# 查询所有POST请求的操作记录
curl -X GET "http://localhost:8080/system/operation-log/page?requestMethod=POST&current=1&size=10" \
  -H "Authorization: Bearer your-token"

# 查询所有DELETE请求的操作记录
curl -X GET "http://localhost:8080/system/operation-log/page?requestMethod=DELETE&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 按客户端IP查询操作记录
```bash
# 查询特定IP的操作记录
curl -X GET "http://localhost:8080/system/operation-log/page?clientIp=*************&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 组合查询示例
```bash
# 查询特定用户的POST请求操作记录
curl -X GET "http://localhost:8080/system/operation-log/page?username=admin&requestMethod=POST&current=1&size=10" \
  -H "Authorization: Bearer your-token"

# 查询特定IP的DELETE操作记录
curl -X GET "http://localhost:8080/system/operation-log/page?clientIp=*************&requestMethod=DELETE&current=1&size=10" \
  -H "Authorization: Bearer your-token"

# 查询失败的POST请求操作记录
curl -X GET "http://localhost:8080/system/operation-log/page?requestMethod=POST&operationStatus=0&current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 获取模块操作统计
```bash
curl -X GET "http://localhost:8080/system/operation-log/statistics/module?days=7" \
  -H "Authorization: Bearer your-token"
```

### 获取操作类型统计
```bash
curl -X GET "http://localhost:8080/system/operation-log/statistics/type?days=7" \
  -H "Authorization: Bearer your-token"
```

### 获取用户操作统计
```bash
curl -X GET "http://localhost:8080/system/operation-log/statistics/user?limit=10&days=7" \
  -H "Authorization: Bearer your-token"
```

### 获取失败操作记录
```bash
curl -X GET "http://localhost:8080/system/operation-log/failures?current=1&size=10" \
  -H "Authorization: Bearer your-token"
```

### 根据IP查询操作记录
```bash
curl -X GET "http://localhost:8080/system/operation-log/ip/*************?limit=50" \
  -H "Authorization: Bearer your-token"
```

### 清理过期操作日志
```bash
curl -X DELETE "http://localhost:8080/system/operation-log/cleanup?days=30" \
  -H "Authorization: Bearer your-token"
```

### 清空所有操作日志
```bash
curl -X DELETE "http://localhost:8080/system/operation-log/clear" \
  -H "Authorization: Bearer your-token"
```

## 最佳实践

### 1. 查询优化
- 使用时间范围限制查询结果
- 合理设置分页大小
- 优先使用索引字段进行查询

### 2. 权限管理
- 严格控制删除权限的授权
- 定期审查权限分配
- 记录权限变更操作

### 3. 数据维护
- 定期清理过期日志数据
- 监控日志数据增长趋势
- 合理设置日志保留策略

### 4. 安全考虑
- 敏感操作增加二次确认
- 重要操作记录详细信息
- 定期备份关键日志数据

### 5. 性能优化
- 大量数据查询使用分页
- 避免无条件的全表查询
- 合理使用缓存机制
