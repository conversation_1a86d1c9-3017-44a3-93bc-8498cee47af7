<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">仪表盘</h1>
      <p class="page-description">欢迎使用 aKey 管理系统</p>
      
      <!-- 菜单加载状态显示 -->
      <div class="menu-status" v-if="menuStore">
        <el-tag v-if="menuStore.isLoaded" type="success" size="small">
          菜单已加载 ({{ menuStore.menus.length }} 项)
        </el-tag>
        <el-tag v-else-if="menuStore.loading" type="warning" size="small">
          菜单加载中...
        </el-tag>
        <el-tag v-else type="info" size="small">
          菜单未加载
        </el-tag>
      </div>

      <!-- SVG 图标示例 -->
      <div class="svg-icon-demo">
        <div class="demo-item">
          <svg-icon name="user" :color="'var(--primary-color)'" />
          <span>用户图标</span>
        </div>
        <div class="demo-item">
          <svg-icon name="add" :color="'var(--success-color)'" />
          <span>添加图标</span>
        </div>
        <div class="demo-item">
          <svg-icon name="add" />
          <span>原始颜色</span>
        </div>
        <div class="demo-item">
          <svg-icon name="api" :color="'var(--warning-color)'" />
          <span>自定义颜色</span>
        </div>
        <div class="demo-item">
          <svg-icon name="api" />
          <span>API原始颜色</span>
        </div>
        <div class="demo-item">
          <svg-icon name="heart" />
          <span>红心原始颜色</span>
        </div>
        <div class="demo-item">
          <svg-icon name="heart" :color="'var(--primary-color)'" />
          <span>红心自定义蓝色</span>
        </div>
        <div class="demo-item">
          <svg-icon name="arrow-right" :color="'var(--danger-color)'" clickable @click="() => ElMessage.info('点击了箭头图标')" />
          <span>可点击箭头</span>
        </div>
      </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card 
        v-for="stat in statsData" 
        :key="stat.title"
        class="stat-card"
        shadow="hover"
      >
        <div class="stat-content">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-trend" :class="stat.trend > 0 ? 'positive' : 'negative'">
              <el-icon :size="12">
                <ArrowUp v-if="stat.trend > 0" />
                <ArrowDown v-else />
              </el-icon>
              {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 图表区域 -->
    <div class="charts-grid">
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>访问趋势</h3>
            <el-button type="primary" size="small">查看详情</el-button>
          </div>
        </template>
        <div class="chart-placeholder">
          <el-icon :size="48" :color="'var(--border-color)'">
            <TrendCharts />
          </el-icon>
          <p>图表区域 - 可集成 ECharts 或其他图表库</p>
        </div>
      </el-card>
      
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>用户分布</h3>
            <el-button type="primary" size="small">查看详情</el-button>
          </div>
        </template>
        <div class="chart-placeholder">
          <el-icon :size="48" :color="'var(--border-color)'">
            <PieChart />
          </el-icon>
          <p>饼图区域 - 用户地域分布统计</p>
        </div>
      </el-card>
    </div>
    
    <!-- 快速操作 -->
    <div class="quick-actions">
      <el-card shadow="hover">
        <template #header>
          <h3>快速操作</h3>
        </template>
        <div class="actions-grid">
          <div 
            v-for="action in quickActions" 
            :key="action.title"
            class="action-item"
            @click="handleQuickAction(action)"
          >
            <div class="action-icon" :style="{ background: action.color }">
              <el-icon :size="20">
                <component :is="action.icon" />
              </el-icon>
            </div>
            <div class="action-title">{{ action.title }}</div>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 最新动态 -->
    <div class="recent-activities">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <h3>最新动态</h3>
            <el-button link size="small">查看全部</el-button>
          </div>
        </template>
        <div class="activities-list">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-avatar">
              <el-avatar :size="32" :src="activity.avatar" />
            </div>
            <div class="activity-content">
              <div class="activity-text">{{ activity.text }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useMenuStore } from '@/stores/menu'
import {
  User,
  Document,
  View,
  DataAnalysis,
  ArrowUp,
  ArrowDown,
  TrendCharts,
  PieChart,
  Plus,
  Edit,
  Setting,
  Upload
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'Dashboard',
  components: {
    User,
    Document,
    View,
    DataAnalysis,
    ArrowUp,
    ArrowDown,
    TrendCharts,
    PieChart,
    Plus,
    Edit,
    Setting,
    Upload
  },
  setup() {
    const menuStore = useMenuStore()

    // 统计数据
    const statsData = ref([
      {
        title: '总用户数',
        value: '12,345',
        icon: 'User',
        color: 'linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%)',
        trend: 12.5
      },
      {
        title: '文章数量',
        value: '1,234',
        icon: 'Document',
        color: 'linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%)',
        trend: 8.2
      },
      {
        title: '今日访问',
        value: '8,765',
        icon: 'View',
        color: 'linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%)',
        trend: -2.1
      },
      {
        title: '数据分析',
        value: '98.5%',
        icon: 'DataAnalysis',
        color: 'linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%)',
        trend: 5.7
      }
    ])
    
    // 快速操作
    const quickActions = ref([
      {
        title: '新建用户',
        icon: 'Plus',
        color: 'linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%)',
        action: 'create-user'
      },
      {
        title: '发布文章',
        icon: 'Edit',
        color: 'linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%)',
        action: 'create-article'
      },
      {
        title: '系统设置',
        icon: 'Setting',
        color: 'linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%)',
        action: 'system-settings'
      },
      {
        title: '数据导入',
        icon: 'Upload',
        color: 'linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%)',
        action: 'data-import'
      },
      {
        title: 'SVG图标测试',
        icon: 'View',
        color: 'linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%)',
        action: 'svg-icon-test'
      }
    ])
    
    // 最新动态
    const recentActivities = ref([
      {
        id: 1,
        text: '管理员 创建了新用户 "张三"',
        time: '2分钟前',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      {
        id: 2,
        text: '编辑 发布了文章 "Vue 3 最佳实践"',
        time: '15分钟前',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      {
        id: 3,
        text: '系统 自动备份数据库完成',
        time: '1小时前',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      },
      {
        id: 4,
        text: '用户 "李四" 更新了个人信息',
        time: '2小时前',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
      }
    ])
    
    // 快速操作处理
    const handleQuickAction = (action) => {
      if (action.action === 'svg-icon-test') {
        // 跳转到 SVG 图标测试页面
        window.open('/test/svg-icon', '_blank')
      } else {
        ElMessage.success(`执行操作: ${action.title}`)
      }
    }
    
    return {
      menuStore,
      statsData,
      quickActions,
      recentActivities,
      handleQuickAction
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;

  .page-title {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-color-primary);
    margin: 0 0 8px 0;
  }

  .page-description {
    font-size: 16px;
    color: var(--text-color-secondary);
    margin: 0 0 12px 0;
  }

  .menu-status {
    margin-top: 8px;
  }

  .svg-demo-header {
    margin-top: 16px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color-primary);
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--text-color-secondary);
    }
  }

  .svg-icon-demo {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 16px;
    padding: 20px;
    background: var(--bg-color-page);
    border-radius: 8px;
    border: 1px solid var(--border-color);

    .demo-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid var(--border-color);
      min-width: 80px;

      span {
        font-size: 11px;
        color: var(--text-color-secondary);
        text-align: center;
        line-height: 1.2;
      }
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  
  .stat-card {
    border: none;
    border-radius: var(--border-radius-xl);
    
    :deep(.el-card__body) {
      padding: 24px;
    }
    
    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: var(--border-radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
      }
      
      .stat-info {
        flex: 1;
        
        .stat-value {
          font-size: 32px;
          font-weight: 700;
          color: var(--text-color-primary);
          line-height: 1;
          margin-bottom: 4px;
        }
        
        .stat-title {
          font-size: 14px;
          color: var(--text-color-secondary);
          margin-bottom: 8px;
        }
        
        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 500;
          
          &.positive {
            color: var(--success-color);
          }
          
          &.negative {
            color: var(--danger-color);
          }
        }
      }
    }
  }
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  
  .chart-card {
    border: none;
    border-radius: var(--border-radius-xl);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-color-primary);
      }
    }
    
    .chart-placeholder {
      height: 300px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--text-color-secondary);
      
      p {
        margin: 16px 0 0 0;
        font-size: 14px;
      }
    }
  }
}

.quick-actions {
  margin-bottom: 32px;
  
  .el-card {
    border: none;
    border-radius: var(--border-radius-xl);
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color-primary);
    }
    
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      
      .action-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        border-radius: var(--border-radius-lg);
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid var(--border-color);
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--box-shadow-md);
          border-color: var(--primary-color);
        }
        
        .action-icon {
          width: 48px;
          height: 48px;
          border-radius: var(--border-radius-lg);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }
        
        .action-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-color-primary);
        }
      }
    }
  }
}

.recent-activities {
  .el-card {
    border: none;
    border-radius: var(--border-radius-xl);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-color-primary);
      }
    }
    
    .activities-list {
      .activity-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px 0;
        border-bottom: 1px solid var(--border-color-lighter);
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-avatar {
          flex-shrink: 0;
        }
        
        .activity-content {
          flex: 1;
          
          .activity-text {
            font-size: 14px;
            color: var(--text-color-primary);
            margin-bottom: 4px;
          }
          
          .activity-time {
            font-size: 12px;
            color: var(--text-color-secondary);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
