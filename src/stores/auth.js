import { defineStore } from 'pinia'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api'
import { getUserInfo } from '@/api/user'
import { usePermissionStore } from './permission'
import { useMenuStore } from './menu'
import { clearUserDataFromStorage } from '@/utils/clearUserData'
import googleAuthSetupManager from '@/utils/googleAuthSetupManager'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)
  const userInfoLoaded = ref(false) // 用户信息是否已加载
  const captchaData = ref({
    captchaId: '',
    imageBase64: ''
  })
  const loading = ref(false)

  // 获取验证码
  const getCaptcha = async () => {
    try {
      const response = await authApi.getCaptcha()
      captchaData.value = {
        captchaId: response.captchaId,
        imageBase64: response.imageBase64
      }
      return response
    } catch (error) {
      console.error('获取验证码失败:', error)
      throw error
    }
  }

  // 获取用户信息
  const fetchUserInfo = async (forceRefresh = false) => {
    // 如果已经加载过且不是强制刷新，直接返回
    if (!forceRefresh && userInfoLoaded.value && userInfo.value) {
      return userInfo.value
    }

    try {
      const response = await getUserInfo()
      userInfo.value = response
      userInfoLoaded.value = true

      // 检查用户是否需要设置谷歌验证码
      checkGoogleAuthSetup(response)

      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      userInfoLoaded.value = false
      throw error
    }
  }

  // 检查谷歌验证码设置状态
  const checkGoogleAuthSetup = (userInfoData) => {
    try {
      // 检查是否应该显示提醒（基于用户之前的选择）
      if (!googleAuthSetupManager.shouldShowReminder()) {
        return
      }

      // 延迟检查，确保页面已经完全加载
      setTimeout(() => {
        googleAuthSetupManager.checkGoogleAuthSetup(userInfoData)
      }, 1000)
    } catch (error) {
      console.warn('检查谷歌验证码设置状态失败:', error)
    }
  }

  // 登录
  const login = async (loginData) => {
    try {
      loading.value = true

      // 开发环境测试：模拟需要谷歌验证码的情况
      if (import.meta.env.DEV && loginData.username === 'test' && !loginData.googleAuthCode) {
        // 模拟后端返回错误码9999
        const mockError = new Error('需要谷歌验证码')
        mockError.response = {
          data: {
            code: 9999,
            message: '该用户已启用谷歌验证器，请输入谷歌验证码'
          }
        }
        throw mockError
      }

      // 构造登录请求数据
      const requestData = {
        username: loginData.username,
        password: loginData.password,
        captchaId: captchaData.value.captchaId,
        captcha: loginData.captcha
      }

      // 如果有谷歌验证码，添加到请求数据中
      if (loginData.googleAuthCode) {
        requestData.googleAuthCode = loginData.googleAuthCode
      }

      const response = await authApi.login(requestData)

      // 检查业务状态码
      if (response.code === 9999) {
        // 需要谷歌验证码，直接返回响应，不处理为错误
        return response
      }
    
      if (response.code === -9998) {
        // 谷歌验证码错误，直接返回响应，不处理为错误
        return response
      }

      // 检查登录是否成功
      if (!response.success || !response.token) {
        throw new Error(response.message || '登录失败')
      }

      // 保存token
      token.value = response.token
      localStorage.setItem('token', response.token)

      // 标记用户曾经成功登录过
      localStorage.setItem('hasLoginHistory', 'true')

      // 登录成功后获取用户信息
      try {
        await fetchUserInfo()
      } catch (userInfoError) {
        console.error('获取用户信息失败:', userInfoError)
        // 获取用户信息失败不影响登录流程，但记录错误
      }

      ElMessage.success('登录成功')
      return response
    } catch (error) {
      console.error('登录失败:', error)

      // 检查是否是谷歌验证码相关的错误
      const isGoogleAuthError = error?.code === -9998 || error?.code === 9999
      if(isGoogleAuthError){
        ElMessage.error('谷歌验证码错误，请重新输入')
      }
      // 只有非谷歌验证码错误才刷新验证码
      if (!isGoogleAuthError) {
        await getCaptcha()
      }

      throw error
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async (showMessage = true) => {
    try {
      // 调用退出登录API
      await authApi.logout()
    } catch (error) {
      console.error('退出登录API调用失败:', error)
      // 即使API调用失败，也要清除本地状态
    }

    // 使用统一的清理方法清除所有用户相关数据
    clearAllUserData()

    if (showMessage) {
      ElMessage.success('已退出登录')
    }
  }

  // 检查是否已登录
  const isLoggedIn = () => {
    return !!token.value
  }

  // 保存重定向路径
  const setRedirectPath = (path) => {
    if (path && path !== '/login' && path !== '/session-expired') {
      localStorage.setItem('redirectPath', path)
    }
  }

  // 获取重定向路径
  const getRedirectPath = () => {
    const path = localStorage.getItem('redirectPath')
    localStorage.removeItem('redirectPath') // 获取后立即清除
    return path || '/'
  }

  // 清除重定向路径
  const clearRedirectPath = () => {
    localStorage.removeItem('redirectPath')
  }

  // 清除登录历史（用于完全重置用户状态）
  const clearLoginHistory = () => {
    localStorage.removeItem('hasLoginHistory')
  }

  /**
   * 统一清理所有用户相关数据
   * 包括：token、用户信息、权限、登录历史、重定向路径等
   * 注意：保留记住我功能的账号密码信息
   * @param {boolean} showMessage 是否显示清理成功消息，默认为false
   * @param {boolean} clearStorage 是否清理localStorage，默认为true
   */
  const clearAllUserData = (showMessage = false, clearStorage = true) => {
    try {
      // 1. 清除store中的状态
      token.value = ''
      userInfo.value = null
      userInfoLoaded.value = false

      // 2. 清除权限数据
      const permissionStore = usePermissionStore()
      permissionStore.clearPermissions()

      // 3. 清除菜单数据
      const menuStore = useMenuStore()
      menuStore.clearMenus()

      // 4. 保留记住我的账号密码，不清理logStore数据

      // 5. 清除localStorage中的用户数据（如果需要，但保留账号密码）
      if (clearStorage) {
        clearUserDataFromStorage()
      }

      if (showMessage) {
        ElMessage.success('用户数据已清理（保留登录信息）')
      }


    } catch (error) {
      console.error('清理用户数据时发生错误:', error)
      if (showMessage) {
        ElMessage.error('数据清理失败，请刷新页面重试')
      }
    }
  }

  return {
    // 状态
    token,
    userInfo,
    userInfoLoaded,
    captchaData,
    loading,

    // 方法
    getCaptcha,
    fetchUserInfo,
    login,
    logout,
    isLoggedIn,
    setRedirectPath,
    getRedirectPath,
    clearRedirectPath,
    clearLoginHistory,
    clearAllUserData
  }
})
