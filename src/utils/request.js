import axios from 'axios'
import { ElMessage } from 'element-plus'
import { rsaCrypto } from './crypto/rsa'
import { handle401Error } from './clearUserData'
import googleAuthManager from './googleAuthManager'

// 安全配置
const SECURITY_CONFIG = {
  enabled: import.meta.env.VITE_SECURITY_ENABLED === 'true',
  publicKey: import.meta.env.VITE_PUBLIC_KEY,
  privateKey: import.meta.env.VITE_PRIVATE_KEY
}

/**
 * 检查是否需要加密
 * @param {string} method - 请求方法
 * @returns {boolean}
 */
const shouldEncrypt = (method) => {
  return SECURITY_CONFIG.enabled &&
         SECURITY_CONFIG.publicKey &&
         ['post', 'put'].includes(method?.toLowerCase())
}

/**
 * 加密请求数据
 * @param {any} data - 请求数据
 * @returns {string} 加密后的数据
 */
const encryptRequestData = (data) => {
  try {
    const jsonString = JSON.stringify(data)
    return rsaCrypto.encryptWithPublicKey(jsonString, SECURITY_CONFIG.publicKey)
  } catch (error) {
    ElMessage.error('客户端数据处理异常，请重试!')
    throw new Error('客户端数据处理异常，请重试!')
  }
}

/**
 * 解密响应数据
 * @param {string} encryptedData - 加密的响应数据
 * @returns {any} 解密后的数据
 */
const decryptResponseData = (encryptedData) => {
  try {
    if (!SECURITY_CONFIG.privateKey) {
      throw new Error('密钥未配置')
    }
    const decryptedString = rsaCrypto.decryptWithPrivateKey(encryptedData, SECURITY_CONFIG.privateKey)
    return JSON.parse(decryptedString)
  } catch (error) {
    ElMessage.error('服务端数据处理异常，请重试!')
    throw new Error('服务端数据处理异常，请重试!')
  }
}

/**
 * 检查响应是否需要解密
 * @param {Object} response - 响应对象
 * @returns {boolean}
 */
const shouldDecrypt = (response) => {
  return SECURITY_CONFIG.enabled &&
         SECURITY_CONFIG.privateKey &&
         (response.headers['X-Encrypted-Response'] === 'true' ||
          response.headers['x-encrypted-response'] === 'true')
}

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 检查是否需要加密
    if (shouldEncrypt(config.method) && config.data) {
      try {
        // 加密请求数据
        const encryptedData = encryptRequestData(config.data)
        config.data = encryptedData

        // 添加加密标识头
        config.headers['X-Decrypted-Request'] = 'true'
        config.headers['Content-Type'] = 'text/plain'
      } catch (error) {
        ElMessage.error('服务端数据处理异常，请重试!')
        return Promise.reject(error)
      }
    }

    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

/**
 * 处理API接口需要谷歌验证码的情况
 * @param {Object} response 原始响应对象
 * @returns {Promise} 返回重试请求的Promise
 */
const handleGoogleAuthRequired = (response) => {
  const originalConfig = response.config

  // 创建重试函数
  const retryRequest = (configWithAuth) => {
    return request(configWithAuth)
  }

  // 使用谷歌验证码管理器处理
  return googleAuthManager.showAuthDialog(originalConfig, retryRequest)
}

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    let { data } = response    
    // 检查是否需要解密响应数据
    if (shouldDecrypt(response)) {
      try {
        data = decryptResponseData(data)
       
      } catch (error) {
        ElMessage.error('服务端数据处理异常，请重试!')
        return Promise.reject(error)
      }
    }

    // 如果后端返回的数据结构是 { code, data, message }
    if (data && typeof data === 'object' && 'code' in data) {
      if (data.code === 200 || data.code === 0) {
        // 对于登录接口，返回完整的响应对象以便获取token等信息
        if (data.data && data.data.token) {
          return { ...data, token: data.data.token }
        }
        return data.data || data
      } else if (data.code === -9999) {
        // API接口需要谷歌验证码二次验证
        return handleGoogleAuthRequired(response)
      } else if (data.code === -9998) {
        // 谷歌验证码错误，需要重新验证
        return Promise.reject(data)
      } else if (data.code === 9999) {
        // 特殊业务状态码：需要谷歌验证码，不当作错误处理
        return data
      } else if (data.code === 9998) {
        // 特殊业务状态码：谷歌验证码错误，不当作错误处理
        return data
      } else {
        ElMessage.error(data.message || '系统异常，请重试')
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    }

    return data
  },
  (error) => {
    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data?.message || '请求参数错误'
          break
        case 401:
          // 使用统一的401错误处理逻辑
          message = handle401Error()
          break
        case 403:
          message = data?.message || '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      message = '网络连接失败'
    } else {
      message = error.message || '请求配置错误'
    }

    // 特殊错误码不显示错误提示（这些是正常的业务流程）
    const specialErrorCodes = [9999] // 9999: 需要谷歌验证码
    const errorCode = error.response?.data?.code

    if (!specialErrorCodes.includes(errorCode)) {
      ElMessage.error(message)
    }

    return Promise.reject(error)
  }
)

// 封装的请求方法
const http = {
  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {Object} params - 请求参数
   * @param {Object} config - 请求配置
   * @returns {Promise}
   */
  get(url, params = {}, config = {}) {
    return request({
      method: 'get',
      url,
      params,
      ...config
    })
  },

  /**
   * POST请求（支持RSA加密）
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise}
   */
  post(url, data = {}, config = {}) {
    return request({
      method: 'post',
      url,
      data,
      ...config
    })
  },

  /**
   * PUT请求（支持RSA加密）
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise}
   */
  put(url, data = {}, config = {}) {
    return request({
      method: 'put',
      url,
      data,
      ...config
    })
  },

  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} config - 请求配置
   * @returns {Promise}
   */
  delete(url, config = {}) {
    return request({
      method: 'delete',
      url,
      ...config
    })
  },

  /**
   * PATCH请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} config - 请求配置
   * @returns {Promise}
   */
  patch(url, data = {}, config = {}) {
    return request({
      method: 'patch',
      url,
      data,
      ...config
    })
  }
}

// 导出原始axios实例和封装的方法
export default request
export { http, SECURITY_CONFIG }
