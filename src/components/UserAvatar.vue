<template>
  <div 
    class="user-avatar" 
    :style="{ backgroundColor: avatarColor, width: size + 'px', height: size + 'px' }"
    :title="username"
  >
    {{ firstLetter }}
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  username: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    default: 32
  }
})

// 预设颜色数组
const colors = [
  '#1890ff', // 蓝色
  '#52c41a', // 绿色
  '#fa8c16', // 橙色
  '#722ed1', // 紫色
  '#eb2f96', // 粉色
  '#13c2c2', // 青色
  '#f5222d', // 红色
  '#faad14', // 金色
  '#2f54eb', // 靛蓝
  '#a0d911'  // 青绿
]

// 获取用户名首字母
const firstLetter = computed(() => {
  if (!props.username) return '?'
  return props.username.charAt(0).toUpperCase()
})

// 基于用户名生成固定颜色
const avatarColor = computed(() => {
  if (!props.username) return colors[0]
  
  // 使用用户名的字符码总和来确定颜色索引
  let hash = 0
  for (let i = 0; i < props.username.length; i++) {
    hash += props.username.charCodeAt(i)
  }
  
  return colors[hash % colors.length]
})
</script>

<style scoped>
.user-avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
  cursor: default;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 根据尺寸调整字体大小 */
.user-avatar[style*="width: 40px"] {
  font-size: 16px;
}

.user-avatar[style*="width: 24px"] {
  font-size: 12px;
}
</style>
