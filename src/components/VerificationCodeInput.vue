<template>
  <div class="verification-code-input">
    <div class="code-inputs" ref="codeInputsRef">
      <input
        v-for="(digit, index) in digits"
        :key="index"
        :ref="el => setInputRef(el, index)"
        v-model="digits[index]"
        type="text"
        inputmode="numeric"
        pattern="[0-9]*"
        maxlength="1"
        class="code-input"
        :class="{ 'has-value': digits[index] }"
        @input="handleInput(index, $event)"
        @keydown="handleKeydown(index, $event)"
        @paste="handlePaste($event)"
        @focus="handleFocus(index)"
        @blur="handleBlur(index)"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick, onMounted } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  length: {
    type: Number,
    default: 6
  },
  autoSubmit: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'complete', 'change'])

// 响应式数据
const digits = reactive(Array(props.length).fill(''))
const inputRefs = ref([])
const codeInputsRef = ref()
const focusedIndex = ref(-1)

// 设置输入框引用
const setInputRef = (el, index) => {
  if (el) {
    inputRefs.value[index] = el
  }
}

// 处理输入
const handleInput = (index, event) => {
  const value = event.target.value
  
  // 只允许数字
  if (!/^\d*$/.test(value)) {
    event.target.value = digits[index]
    return
  }
  
  // 更新数字
  digits[index] = value
  
  // 如果输入了数字，移动到下一个输入框
  if (value && index < props.length - 1) {
    focusNext(index + 1)
  }
  
  updateModelValue()
}

// 处理键盘事件
const handleKeydown = (index, event) => {
  const { key } = event
  
  // 处理退格键
  if (key === 'Backspace') {
    if (!digits[index] && index > 0) {
      // 如果当前输入框为空，移动到上一个输入框并清空
      digits[index - 1] = ''
      focusPrev(index - 1)
      updateModelValue()
    } else if (digits[index]) {
      // 如果当前输入框有值，清空当前输入框
      digits[index] = ''
      updateModelValue()
    }
    return
  }
  
  // 处理删除键
  if (key === 'Delete') {
    digits[index] = ''
    updateModelValue()
    return
  }
  
  // 处理左右箭头键
  if (key === 'ArrowLeft' && index > 0) {
    focusPrev(index - 1)
    return
  }
  
  if (key === 'ArrowRight' && index < props.length - 1) {
    focusNext(index + 1)
    return
  }
  
  // 只允许数字键
  if (!/^\d$/.test(key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(key)) {
    event.preventDefault()
  }
}

// 处理粘贴
const handlePaste = (event) => {
  event.preventDefault()
  const pastedData = event.clipboardData.getData('text')
  const numbers = pastedData.replace(/\D/g, '').slice(0, props.length)
  
  if (numbers.length > 0) {
    // 清空所有输入框
    digits.fill('')
    
    // 填充粘贴的数字
    for (let i = 0; i < numbers.length; i++) {
      digits[i] = numbers[i]
    }
    
    // 焦点移动到最后一个填充的输入框的下一个位置
    const nextIndex = Math.min(numbers.length, props.length - 1)
    focusNext(nextIndex)
    
    updateModelValue()
  }
}

// 处理焦点
const handleFocus = (index) => {
  focusedIndex.value = index
}

const handleBlur = (index) => {
  if (focusedIndex.value === index) {
    focusedIndex.value = -1
  }
}

// 焦点控制
const focusNext = (index) => {
  if (index < props.length && inputRefs.value[index]) {
    nextTick(() => {
      inputRefs.value[index].focus()
    })
  }
}

const focusPrev = (index) => {
  if (index >= 0 && inputRefs.value[index]) {
    nextTick(() => {
      inputRefs.value[index].focus()
    })
  }
}

// 更新模型值
const updateModelValue = () => {
  const code = digits.join('')
  emit('update:modelValue', code)
  emit('change', code)
  
  // 检查是否完成输入
  if (code.length === props.length) {
    emit('complete', code)
    
    // 自动提交
    if (props.autoSubmit) {
      nextTick(() => {
        emit('submit', code)
      })
    }
  }
}

// 公共方法
const focus = () => {
  if (inputRefs.value[0]) {
    inputRefs.value[0].focus()
  }
}

const clear = () => {
  digits.fill('')
  updateModelValue()
  focus()
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  const cleanValue = (newValue || '').replace(/\D/g, '').slice(0, props.length)
  
  // 清空所有输入框
  digits.fill('')
  
  // 填充新值
  for (let i = 0; i < cleanValue.length; i++) {
    digits[i] = cleanValue[i]
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  focus,
  clear
})

// 组件挂载后自动聚焦第一个输入框
onMounted(() => {
  if (!props.disabled) {
    focus()
  }
})
</script>

<style scoped>
.verification-code-input {
  display: flex;
  justify-content: center;
  align-items: center;
}

.code-inputs {
  display: flex;
  gap: 8px;
  align-items: center;
}

.code-input {
  width: 48px;
  height: 48px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-primary);
  background: var(--bg-color-page);
  transition: all 0.2s ease;
  outline: none;
}

.code-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.code-input.has-value {
  border-color: var(--success-color);
  background: var(--bg-color-overlay);
}

.code-input:disabled {
  background: var(--bg-color-disabled);
  color: var(--text-color-disabled);
  border-color: var(--border-color-disabled);
  cursor: not-allowed;
  opacity: 0.6;
}


/* 移动端适配 */
@media (max-width: 768px) {
  .code-inputs {
    gap: 6px;
  }
  
  .code-input {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .code-inputs {
    gap: 4px;
  }

  .code-input {
    width: 36px;
    height: 36px;
    font-size: 16px; /* 保持16px避免iOS Safari自动放大 */
  }
}
</style>
