<template>
  <div></div>
</template>

<script>
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'Redirect',
  setup() {
    const route = useRoute()
    const router = useRouter()

    onMounted(() => {
      const { params, query } = route
      const { path } = params
      
      // 重定向到目标路径
      router.replace({
        path: '/' + path,
        query
      })
    })

    return {}
  }
}
</script>
