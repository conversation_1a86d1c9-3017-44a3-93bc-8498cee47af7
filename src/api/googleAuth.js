import request from '@/utils/request'

/**
 * 谷歌验证器管理 API
 */

/**
 * 生成谷歌验证器设置信息
 * @returns {Promise} 设置信息对象 GoogleAuthSetupVO
 * @description 返回包含密钥、二维码、OTP Auth URL等设置信息
 */
export function getGoogleAuthSetup() {
  return request({
    url: '/google-auth/setup',
    method: 'get'
  })
}

/**
 * 确认设置谷歌验证器
 * @param {Object} data 设置数据
 * @param {string} data.code 6位数字验证码
 * @returns {Promise} 设置结果
 */
export function confirmGoogleAuthSetup(data) {
  return request({
    url: '/google-auth/setup',
    method: 'post',
    data
  })
}

/**
 * 验证谷歌验证码
 * @param {Object} data 验证数据
 * @param {string} data.code 6位数字验证码
 * @returns {Promise} 验证结果
 */
export function verifyGoogleAuth(data) {
  return request({
    url: '/google-auth/verify',
    method: 'post',
    data
  })
}

/**
 * 获取谷歌验证器状态
 * @returns {Promise} 是否已设置谷歌验证器
 */
export function getGoogleAuthStatus() {
  return request({
    url: '/google-auth/status',
    method: 'get'
  })
}

/**
 * 重置谷歌验证器
 * @returns {Promise} 重置结果
 */
export function resetGoogleAuth() {
  return request({
    url: '/google-auth/reset',
    method: 'delete'
  })
}
