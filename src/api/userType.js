import request from '@/utils/request'

/**
 * 用户类型管理 API
 */

/**
 * 分页查询用户类型列表
 * @param {Object} params 查询参数
 * @param {number} params.current 页码，从1开始
 * @param {number} params.size 每页大小
 * @param {string} params.typeName 类型名称，支持模糊查询
 * @param {number} params.isBuiltin 是否内置类型，0=内置，1=非内置
 * @returns {Promise} 分页数据
 */
export function getUserTypePage(params) {
  return request({
    url: '/userType/page',
    method: 'get',
    params
  })
}

/**
 * 获取所有用户类型列表
 * @returns {Promise} 用户类型列表
 */
export function getUserTypeList() {
  return request({
    url: '/userType/list',
    method: 'get'
  })
}

/**
 * 新增用户类型
 * @param {Object} data 用户类型数据
 * @param {string} data.typeName 类型名称
 * @param {number} data.isBuiltin 是否内置类型，0=内置，1=非内置
 * @returns {Promise} 响应结果
 */
export function createUserType(data) {
  return request({
    url: '/userType',
    method: 'post',
    data
  })
}

/**
 * 修改用户类型
 * @param {string} id 用户类型ID
 * @param {Object} data 用户类型数据
 * @param {string} data.typeName 类型名称
 * @param {number} data.isBuiltin 是否内置类型，0=内置，1=非内置
 * @returns {Promise} 响应结果
 */
export function updateUserType(id, data) {
  return request({
    url: `/userType/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除用户类型
 * @param {string} id 用户类型ID
 * @returns {Promise} 响应结果
 */
export function deleteUserType(id) {
  return request({
    url: `/userType/${id}`,
    method: 'delete'
  })
}

/**
 * 获取用户类型已分配的菜单
 * @param {string} id 用户类型ID
 * @returns {Promise} 菜单ID列表
 */
export function getUserTypeMenus(id) {
  return request({
    url: `/userType/${id}/menus`,
    method: 'get'
  })
}

/**
 * 分配菜单权限
 * @param {string} id 用户类型ID
 * @param {Object} data 菜单分配数据
 * @param {Array<string>} data.menuIds 菜单ID列表
 * @returns {Promise} 响应结果
 */
export function assignUserTypeMenus(id, data) {
  return request({
    url: `/userType/${id}/assignMenus`,
    method: 'post',
    data
  })
}
