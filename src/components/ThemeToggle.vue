<template>
  <div 
    class="theme-toggle"
    :class="[
      `theme-toggle--${size}`,
      { 'theme-toggle--dark': themeStore.isDark() }
    ]"
  >
    <el-tooltip
      :content="tooltipText"
      placement="bottom"
      :disabled="!showTooltip"
    >
      <button
        class="theme-toggle-button"
        :aria-label="ariaLabel"
        @click="handleToggle"
      >
        <div class="icon-container">
          <svg-icon
            :name="iconName"
            :size="iconSize"
            :color="iconColor"
            class="theme-icon"
            :class="{ 'rotating': isToggling }"
          />
        </div>
      </button>
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

// Props
const props = defineProps({
  // 组件大小
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 是否显示提示文字
  showTooltip: {
    type: Boolean,
    default: false
  },
  // 自定义图标颜色
  iconColor: {
    type: String,
    default: null
  },
  // 是否圆形按钮
  round: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['toggle'])

// Store
const themeStore = useThemeStore()

// 响应式数据
const isToggling = ref(false)

// 计算属性
const iconName = computed(() => {
  return themeStore.isDark() ? 'sun' : 'moon'
})

const iconSize = computed(() => {
  const sizeMap = {
    small: 'small',
    medium: 'medium', 
    large: 'large'
  }
  return sizeMap[props.size] || 'medium'
})

const tooltipText = computed(() => {
  return themeStore.isDark() ? '切换到浅色主题' : '切换到深色主题'
})

const ariaLabel = computed(() => {
  return themeStore.isDark() ? '切换到浅色主题' : '切换到深色主题'
})

// 方法
const handleToggle = async () => {
  if (isToggling.value) return
  
  isToggling.value = true
  
  try {
    // 切换主题
    themeStore.toggleTheme()
    
    // 触发事件
    emit('toggle', themeStore.currentTheme)
    
    // 添加旋转动画延迟
    setTimeout(() => {
      isToggling.value = false
    }, 300)
  } catch (error) {
    console.error('Theme toggle failed:', error)
    isToggling.value = false
  }
}

// 系统主题监听清理函数
let cleanupSystemThemeWatcher = null

// 生命周期
onMounted(() => {
  // 初始化主题
  themeStore.initTheme()
  
  // 监听系统主题变化
  cleanupSystemThemeWatcher = themeStore.watchSystemTheme()
})

onUnmounted(() => {
  // 清理系统主题监听器
  if (cleanupSystemThemeWatcher) {
    cleanupSystemThemeWatcher()
  }
})
</script>

<style lang="scss" scoped>
.theme-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  // 默认样式
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  &:focus {
    outline: none;
    box-shadow: var(--box-shadow-focus);
  }
  
  // 点击波纹效果
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.4s ease-out, height 0.4s ease-out, opacity 0.4s ease-out;
    opacity: 0;
    z-index: 0;
  }
  
  &:active::before {
    width: 100px;
    height: 100px;
    opacity: 1;
  }
}

.icon-container {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.rotating {
    animation: rotate-theme 0.6s ease-in-out;
  }
}

// 尺寸变体
.theme-toggle--small {
  .theme-toggle-button {
    padding: 6px;
  }
}

.theme-toggle--medium {
  .theme-toggle-button {
    padding: 8px;
  }
}

.theme-toggle--large {
  .theme-toggle-button {
    padding: 10px;
  }
}

// 深色主题下的样式调整
.theme-toggle--dark {
  .theme-toggle-button {
    background: rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.2);
    
    &:hover {
      background: rgba(0, 0, 0, 0.2);
      border-color: rgba(0, 0, 0, 0.3);
    }
    
    &::before {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}

// 旋转动画
@keyframes rotate-theme {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

// 图标颜色过渡
:deep(.svg-icon) {
  transition: color 0.3s ease;
}

// 响应式设计
@media (max-width: 768px) {
  .theme-toggle-button {
    padding: 6px;
  }
  
  .theme-toggle--large .theme-toggle-button {
    padding: 8px;
  }
}
</style>
