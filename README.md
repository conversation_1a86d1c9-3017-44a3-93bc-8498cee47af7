# aKey Web 项目

基于 Vue 3 + Vite + Element Plus 的现代化前端项目，集成了完整的基础框架和RSA加解密功能。

## 🚀 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **Vue Router 4** - 官方路由管理器
- **Pinia** - Vue 3 状态管理库
- **Element Plus** - 基于Vue 3的组件库
- **Axios** - HTTP客户端
- **JSEncrypt** - RSA加解密库
- **Sass** - CSS预处理器

## 📁 项目结构

```
src/
├── api/                  # API接口
│   ├── index.js         # API统一入口
│   ├── auth.js          # 认证API
│   ├── menu.js          # 菜单API
│   └── permission.js    # 权限API
├── assets/              # 静态资源
│   ├── images/          # 图片资源
│   ├── styles/          # 样式文件
│   └── svg/             # SVG图标
├── components/          # 可复用组件
│   ├── Header.vue       # 顶部导航
│   ├── Layout.vue       # 主布局
│   ├── MenuItem.vue     # 菜单项
│   └── Sidebar.vue      # 侧边栏
├── directives/          # 自定义指令
│   ├── index.js         # 指令入口
│   └── permission.js    # 权限指令
├── router/              # 路由配置
│   └── index.js
├── stores/              # Pinia状态管理
│   ├── index.js         # Pinia实例
│   ├── auth.js          # 认证状态
│   ├── layout.js        # 布局状态
│   ├── log.js           # 日志状态
│   ├── menu.js          # 菜单状态
│   └── permission.js    # 权限状态
├── utils/               # 工具函数
│   ├── crypto/          # 加密工具
│   │   ├── index.js     # 加密工具入口
│   │   └── rsa.js       # RSA加解密
│   ├── clearUserData.js # 用户数据清理
│   ├── dynamicRoutes.js # 动态路由
│   ├── menu.js          # 菜单工具
│   ├── permission.js    # 权限工具
│   ├── request.js       # axios配置
│   └── index.js         # 工具函数入口
├── views/               # 页面组件
│   ├── 403/             # 403错误页
│   ├── 404/             # 404错误页
│   ├── auth/            # 认证相关页面
│   ├── error/           # 错误页面
│   ├── system/          # 系统管理
│   ├── user/            # 用户管理
│   ├── Dashboard.vue    # 仪表盘
│   └── Login.vue        # 登录页
├── App.vue              # 根组件
└── main.js              # 入口文件
```

## 🔐 RSA加解密功能

### 特性

- **分段加解密**: 自动处理长文本的分段加解密
- **填充方式**: RSA/ECB/PKCS1Padding
- **密钥长度**: 2048位
- **编码格式**: Base64
- **字符支持**: 支持中文和特殊字符

### 使用方法

#### 1. 基础用法

```javascript
import { rsaCrypto } from '@/utils/crypto'

// 生成密钥对
const keyPair = rsaCrypto.generateKeyPair()

// 加密
const encrypted = rsaCrypto.encryptWithPublicKey('明文数据', keyPair.publicKey)

// 解密
const decrypted = rsaCrypto.decryptWithPrivateKey(encrypted, keyPair.privateKey)
```

#### 2. 实例用法

```javascript
import { rsaUtil } from '@/utils/crypto'

// 设置密钥
rsaUtil.setPublicKey(publicKey)
rsaUtil.setPrivateKey(privateKey)

// 加解密
const encrypted = rsaUtil.encrypt('明文数据')
const decrypted = rsaUtil.decrypt(encrypted)
```

#### 3. 实际项目示例

```javascript
// 加密敏感数据发送到服务器
const sensitiveData = {
  username: 'admin',
  password: 'secret123'
}

const jsonData = JSON.stringify(sensitiveData)
const encrypted = rsaCrypto.encryptWithPublicKey(jsonData, serverPublicKey)

// 发送加密数据到服务器
await api.post('/login', { data: encrypted })
```

### API参考

#### rsaCrypto 对象

- `encryptWithPublicKey(plaintext, publicKey)` - 使用公钥加密
- `decryptWithPrivateKey(ciphertext, privateKey)` - 使用私钥解密
- `generateKeyPair()` - 生成密钥对
- `validateKey(key, type)` - 验证密钥格式

#### rsaUtil 实例

- `setPublicKey(publicKey)` - 设置公钥
- `setPrivateKey(privateKey)` - 设置私钥
- `encrypt(plaintext)` - 加密
- `decrypt(ciphertext)` - 解密

## 🛠️ 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 🔧 配置说明

### 环境变量

- `.env` - 通用配置
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

### API配置

**HTTP请求配置** (`src/utils/request.js`)：
- 请求拦截器：自动添加token、时间戳等
- 响应拦截器：统一错误处理、消息提示
- 超时设置：10秒
- 基础URL：可通过环境变量配置

**API接口** (`src/api/`)：
- `auth.js` - 认证相关接口
- `menu.js` - 菜单相关接口
- `permission.js` - 权限相关接口
- `index.js` - API统一入口

**API使用示例**：
```javascript
// 在组件中使用API
import { authApi, menuApi, permissionApi } from '@/api'

// 用户登录
const loginResult = await authApi.login({ username, password, captcha })

// 获取菜单列表
const menus = await menuApi.getRouters()

// 获取用户权限
const permissions = await permissionApi.getUserPermissions()
```

## 🎯 功能特性

### 已实现功能

- ✅ Vue Router 路由系统
- ✅ Pinia 状态管理
- ✅ Element Plus UI组件
- ✅ Axios HTTP客户端
- ✅ RSA分段加解密
- ✅ 主题切换（亮色/暗色）
- ✅ 响应式布局
- ✅ 路由守卫
- ✅ 错误处理

### 测试功能

在首页可以测试以下功能：

1. **API连接测试** - 测试HTTP请求
2. **主题切换** - 切换亮色/暗色主题
3. **消息提示** - 测试Element Plus消息组件
4. **RSA加解密** - 测试RSA加解密功能

## 📝 开发建议

1. **API集成**: 根据后端接口调整 `src/utils/api/` 中的API配置
2. **权限控制**: 基于用户状态扩展权限系统
3. **组件开发**: 在 `src/components/` 中创建可复用组件
4. **页面开发**: 在 `src/views/` 中添加业务页面
5. **状态管理**: 根据需要在 `src/stores/` 中添加新的store

## 🔒 安全注意事项

1. **密钥管理**: 生产环境中的RSA密钥应由服务器管理
2. **HTTPS**: 生产环境必须使用HTTPS传输
3. **Token安全**: 及时刷新和验证JWT token
4. **输入验证**: 对用户输入进行严格验证

## 📄 许可证

MIT License
