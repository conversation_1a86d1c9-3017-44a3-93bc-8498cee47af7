import { createApp } from 'vue'
import App from '@/App.vue'

// 路由
import router from '@/router/index.js'

// 状态管理
import pinia from '@/stores/index.js'
import { useThemeStore } from '@/stores/theme'

// 导入自定义Element Plus主题样式（必须在Element Plus之前导入）
import '@/styles/element/index.scss'
import '@/styles/element/dark.scss'

// Element Plus
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// Element Plus 中文语言包
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import '@/assets/styles/main.scss'              // 全局样式

// 全局指令
import { setupDirectives } from '@/directives'

// 全局组件
import GlobalComponents from '@/components'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(router)
app.use(pinia)
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(GlobalComponents)

// 注册全局指令
setupDirectives(app)

// 挂载应用
app.mount('#app')

// 初始化主题
const themeStore = useThemeStore()
themeStore.initTheme()
