/**
 * 操作日志API接口
 * 提供操作日志的查询、统计、删除等功能
 */

import request from '@/utils/request'

/**
 * 分页查询操作日志
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码，默认1
 * @param {number} params.size 每页大小，默认10
 * @param {string} params.userId 用户ID
 * @param {string} params.username 用户名（模糊查询）
 * @param {string} params.operationModule 操作模块
 * @param {string} params.operationType 操作类型
 * @param {number} params.operationStatus 操作状态(0-失败,1-成功)
 * @param {string} params.requestMethod 请求方法(GET/POST/PUT/DELETE等)
 * @param {string} params.clientIp 客户端IP地址
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @returns {Promise} 分页查询结果
 */
export function getOperationLogPage(params = {}) {
  return request({
    url: '/system/operation-log/page',
    method: 'get',
    params
  })
}

/**
 * 获取操作日志详情
 * @param {string} id 操作日志ID
 * @returns {Promise} 操作日志详情
 */
export function getOperationLogDetail(id) {
  return request({
    url: `/system/operation-log/${id}`,
    method: 'get'
  })
}

/**
 * 获取用户操作历史记录
 * @param {string} userId 用户ID
 * @param {Object} params 查询参数
 * @param {number} params.limit 限制数量，默认10
 * @returns {Promise} 用户操作历史记录
 */
export function getUserOperationHistory(userId, params = {}) {
  return request({
    url: `/system/operation-log/history/${userId}`,
    method: 'get',
    params
  })
}

/**
 * 获取操作日志统计信息
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间，默认7天前
 * @param {string} params.endTime 结束时间，默认当前时间
 * @returns {Promise} 统计信息
 */
export function getOperationLogStatistics(params = {}) {
  return request({
    url: '/system/operation-log/statistics',
    method: 'get',
    params
  })
}

/**
 * 按模块统计操作数量
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间，默认7天前
 * @param {string} params.endTime 结束时间，默认当前时间
 * @returns {Promise} 模块统计数据
 */
export function getModuleStatistics(params = {}) {
  return request({
    url: '/system/operation-log/statistics/module',
    method: 'get',
    params
  })
}

/**
 * 按操作类型统计操作数量
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间，默认7天前
 * @param {string} params.endTime 结束时间，默认当前时间
 * @returns {Promise} 操作类型统计数据
 */
export function getTypeStatistics(params = {}) {
  return request({
    url: '/system/operation-log/statistics/type',
    method: 'get',
    params
  })
}

/**
 * 按用户统计操作数量
 * @param {Object} params 查询参数
 * @param {string} params.startTime 开始时间，默认7天前
 * @param {string} params.endTime 结束时间，默认当前时间
 * @param {number} params.limit 限制数量，默认10
 * @returns {Promise} 用户统计数据
 */
export function getUserStatistics(params = {}) {
  return request({
    url: '/system/operation-log/statistics/user',
    method: 'get',
    params
  })
}

/**
 * 获取失败操作记录
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码，默认1
 * @param {number} params.size 每页大小，默认10
 * @param {string} params.startTime 开始时间，默认7天前
 * @param {string} params.endTime 结束时间，默认当前时间
 * @returns {Promise} 失败操作记录
 */
export function getFailureOperations(params = {}) {
  return request({
    url: '/system/operation-log/failures',
    method: 'get',
    params
  })
}

/**
 * 根据IP地址查询操作记录
 * @param {string} clientIp 客户端IP
 * @param {Object} params 查询参数
 * @param {number} params.limit 限制数量，默认100
 * @returns {Promise} IP操作记录
 */
export function getOperationsByIp(clientIp, params = {}) {
  return request({
    url: `/system/operation-log/ip/${clientIp}`,
    method: 'get',
    params
  })
}

/**
 * 清理过期操作日志
 * @param {Object} params 清理参数
 * @param {number} params.days 清理多少天前的记录，默认30天
 * @returns {Promise} 清理结果
 */
export function cleanupExpiredOperationLogs(params = {}) {
  return request({
    url: '/system/operation-log/cleanup',
    method: 'delete',
    params
  })
}

/**
 * 清空所有操作日志
 * @returns {Promise} 清空结果
 */
export function clearAllOperationLogs() {
  return request({
    url: '/system/operation-log/clear',
    method: 'delete'
  })
}

// 操作类型枚举
export const OPERATION_TYPES = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  QUERY: 'QUERY',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  EXPORT: 'EXPORT',
  IMPORT: 'IMPORT',
  APPROVE: 'APPROVE',
  REJECT: 'REJECT',
  ENABLE: 'ENABLE',
  DISABLE: 'DISABLE',
  RESET: 'RESET',
  OTHER: 'OTHER'
}

// 操作类型中文映射
export const OPERATION_TYPE_LABELS = {
  CREATE: '新增',
  UPDATE: '修改',
  DELETE: '删除',
  QUERY: '查询',
  LOGIN: '登录',
  LOGOUT: '登出',
  EXPORT: '导出',
  IMPORT: '导入',
  ENABLE: '启用',
  DISABLE: '禁用',
  RESET: '重置',
  OTHER: '其他'
}

// 操作状态枚举
export const OPERATION_STATUS = {
  FAILURE: 0,
  SUCCESS: 1
}

// 操作状态中文映射
export const OPERATION_STATUS_LABELS = {
  0: '失败',
  1: '成功'
}
