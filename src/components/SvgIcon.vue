<template>
  <div 
    class="svg-icon" 
    :class="[
      `svg-icon--${size}`,
      { 'svg-icon--clickable': clickable }
    ]"
    :style="customStyle"
    @click="handleClick"
    v-html="svgContent"
  />
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'

export default {
  name: 'SvgIcon',
  props: {
    // 图标名称（不需要 .svg 后缀）
    name: {
      type: String,
      required: true
    },
    // 图标大小预设
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large', 'xlarge'].includes(value)
    },
    // 自定义宽度
    width: {
      type: [String, Number],
      default: null
    },
    // 自定义高度
    height: {
      type: [String, Number],
      default: null
    },
    // 图标颜色
    color: {
      type: String,
      default: null
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    // 是否禁用缓存
    noCache: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const svgContent = ref('')
    const svgCache = new Map()

    // 计算自定义样式
    const customStyle = computed(() => {
      const style = {}
      
      if (props.width) {
        style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
      }
      
      if (props.height) {
        style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
      }
      
      if (props.color) {
        style.color = props.color
      }
      
      return style
    })

    // 加载 SVG 文件
    const loadSvg = async (iconName) => {
      if (!iconName) {
        svgContent.value = ''
        return
      }

      // 检查缓存
      if (!props.noCache && svgCache.has(iconName)) {
        svgContent.value = svgCache.get(iconName)
        return
      }

      try {
        // 动态导入 SVG 文件
        const svgModule = await import(`@/assets/svg/${iconName}.svg?raw`)
        let content = svgModule.default
        // 处理 SVG 内容，只有设置了 color 属性时才替换颜色
        if (content && typeof content === 'string' && props.color) {
          // 只有当用户设置了 color 属性时，才将颜色替换为 currentColor
          // content = content.replace(/fill="[^"]*"/g, 'fill="currentColor"')
          content = content.replace(/stroke="[^"]*"/g, 'stroke="currentColor"')

          // 确保 SVG 具有正确的属性
          if (!content.includes('fill=') && !content.includes('stroke=')) {
            content = content.replace('<svg', '<svg fill="currentColor"')
          }
        }

        svgContent.value = content

        // 缓存结果
        if (!props.noCache) {
          svgCache.set(iconName, content)
        }
      } catch (error) {
        console.warn(`Failed to load SVG icon: ${iconName}`, error)
        svgContent.value = `<svg viewBox="0 0 24 24" fill="currentColor"><text x="12" y="12" text-anchor="middle" dominant-baseline="middle" font-size="12">?</text></svg>`
      }
    }

    // 处理点击事件
    const handleClick = (event) => {
      if (props.clickable) {
        emit('click', event)
      }
    }

    // 监听 name 属性变化
    watch(() => props.name, (newName) => {
      loadSvg(newName)
    }, { immediate: true })

    onMounted(() => {
      loadSvg(props.name)
    })

    return {
      svgContent,
      customStyle,
      handleClick
    }
  }
}
</script>

<style scoped>
.svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  color: inherit;
  transition: all 0.2s ease;
}

/* 尺寸预设 */
.svg-icon--small {
  width: 16px;
  height: 16px;
}

.svg-icon--medium {
  width: 20px;
  height: 20px;
}

.svg-icon--large {
  width: 24px;
  height: 24px;
}

.svg-icon--xlarge {
  width: 32px;
  height: 32px;
}

/* 可点击状态 */
.svg-icon--clickable {
  cursor: pointer;
}

.svg-icon--clickable:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

.svg-icon--clickable:active {
  transform: scale(0.95);
}

/* 确保 SVG 元素继承容器样式 */
.svg-icon :deep(svg) {
  width: 100%;
  height: 100%;
  display: block;
}
</style>
