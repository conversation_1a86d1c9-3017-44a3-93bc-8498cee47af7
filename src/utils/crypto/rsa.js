import JSEncrypt from 'jsencrypt'

/**
 * RSA加解密工具类
 * 支持分段加解密，填充方式：RSA/ECB/PKCS1Padding，密钥长度：2048
 */
class RSAUtil {
  constructor() {
    this.keySize = 2048 // 密钥长度
    this.maxEncryptSize = 245 // 2048位密钥最大加密长度 (2048/8 - 11)
    this.maxDecryptSize = 256 // 2048位密钥解密块大小 (2048/8)
  }

  /**
   * 设置公钥
   * @param {string} publicKey - PEM格式的公钥
   */
  setPublicKey(publicKey) {
    this.publicKey = publicKey
    this.encryptor = new JSEncrypt()
    this.encryptor.setPublicKey(publicKey)
  }

  /**
   * 设置私钥
   * @param {string} privateKey - PEM格式的私钥
   */
  setPrivateKey(privateKey) {
    this.privateKey = privateKey
    this.decryptor = new JSEncrypt()
    this.decryptor.setPrivateKey(privateKey)
  }

  /**
   * 字符串转字节数组
   * @param {string} str - 输入字符串
   * @returns {Uint8Array} 字节数组
   */
  stringToBytes(str) {
    return new TextEncoder().encode(str)
  }

  /**
   * 字节数组转字符串
   * @param {Uint8Array} bytes - 字节数组
   * @returns {string} 字符串
   */
  bytesToString(bytes) {
    return new TextDecoder().decode(bytes)
  }

  /**
   * 字节数组转Base64
   * @param {Uint8Array} bytes - 字节数组
   * @returns {string} Base64字符串
   */
  bytesToBase64(bytes) {
    let binary = ''
    for (let i = 0; i < bytes.length; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * Base64转字节数组
   * @param {string} base64 - Base64字符串
   * @returns {Uint8Array} 字节数组
   */
  base64ToBytes(base64) {
    const binary = atob(base64)
    const bytes = new Uint8Array(binary.length)
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i)
    }
    return bytes
  }

  /**
   * 分段加密
   * @param {string} plaintext - 明文
   * @returns {string} 加密后的Base64字符串
   */
  encrypt(plaintext) {
    if (!this.encryptor) {
      throw new Error('请先设置公钥')
    }

    try {
      const bytes = this.stringToBytes(plaintext)
      const allEncryptedBytes = []

      // 分段加密
      for (let i = 0; i < bytes.length; i += this.maxEncryptSize) {
        const block = bytes.slice(i, i + this.maxEncryptSize)
        const blockStr = this.bytesToString(block)
        const encryptedBlock = this.encryptor.encrypt(blockStr)

        if (!encryptedBlock) {
          throw new Error(`加密失败：第${Math.floor(i / this.maxEncryptSize) + 1}段`)
        }

        // 将Base64解码为字节数组，然后合并到总字节数组中
        const encryptedBytes = this.base64ToBytes(encryptedBlock)
        allEncryptedBytes.push(...encryptedBytes)
      }

      // 将所有加密字节转换为Base64（与Java后端保持一致）
      console.log(this.bytesToBase64(new Uint8Array(allEncryptedBytes)))
      return this.bytesToBase64(new Uint8Array(allEncryptedBytes))
    } catch (error) {
      console.error('RSA加密失败:', error)
      throw new Error(`RSA加密失败: ${error.message}`)
    }
  }

  /**
   * 分段解密
   * @param {string} ciphertext - 加密后的Base64字符串
   * @returns {string} 解密后的明文
   */
  decrypt(ciphertext) {
    if (!this.decryptor) {
      throw new Error('请先设置私钥')
    }

    try {
      // 从Base64解码为字节数组
      const encryptedBytes = this.base64ToBytes(ciphertext)
      const decryptedBlocks = []

      // 按固定块大小分段解密（与Java后端保持一致）
      for (let offset = 0; offset < encryptedBytes.length; offset += this.maxDecryptSize) {
        const blockSize = Math.min(this.maxDecryptSize, encryptedBytes.length - offset)
        const block = encryptedBytes.slice(offset, offset + blockSize)

        // 将字节数组转换为Base64字符串进行解密
        const blockBase64 = this.bytesToBase64(block)
        const decryptedBlock = this.decryptor.decrypt(blockBase64)

        if (decryptedBlock === false || decryptedBlock === null) {
          throw new Error(`解密失败：第${Math.floor(offset / this.maxDecryptSize) + 1}段`)
        }

        decryptedBlocks.push(decryptedBlock)
      }

      // 合并所有解密块
      return decryptedBlocks.join('')
    } catch (error) {
      console.error('RSA解密失败:', error)
      throw new Error(`RSA解密失败: ${error.message}`)
    }
  }

  /**
   * 生成RSA密钥对
   * @returns {Object} 包含公钥和私钥的对象
   */
  generateKeyPair() {
    const crypt = new JSEncrypt({ default_key_size: this.keySize })
    return {
      publicKey: crypt.getPublicKey(),
      privateKey: crypt.getPrivateKey()
    }
  }

  /**
   * 验证密钥格式
   * @param {string} key - 密钥字符串
   * @param {string} type - 密钥类型 'public' 或 'private'
   * @returns {boolean} 是否有效
   */
  validateKey(key, type) {
    try {
      const crypt = new JSEncrypt()
      if (type === 'public') {
        crypt.setPublicKey(key)
        return crypt.getPublicKey() !== false
      } else if (type === 'private') {
        crypt.setPrivateKey(key)
        return crypt.getPrivateKey() !== false
      }
      return false
    } catch (error) {
      return false
    }
  }
}

// 创建单例实例
const rsaUtil = new RSAUtil()

// 导出工具函数
export {
  rsaUtil,
  RSAUtil
}

// 便捷方法
export const rsaCrypto = {
  /**
   * 设置公钥并加密
   * @param {string} plaintext - 明文
   * @param {string} publicKey - 公钥
   * @returns {string} 加密结果
   */
  encryptWithPublicKey(plaintext, publicKey) {
    rsaUtil.setPublicKey(publicKey)
    return rsaUtil.encrypt(plaintext)
  },

  /**
   * 设置私钥并解密
   * @param {string} ciphertext - 密文
   * @param {string} privateKey - 私钥
   * @returns {string} 解密结果
   */
  decryptWithPrivateKey(ciphertext, privateKey) {
    rsaUtil.setPrivateKey(privateKey)
    return rsaUtil.decrypt(ciphertext)
  },

  /**
   * 生成密钥对
   * @returns {Object} 密钥对
   */
  generateKeyPair() {
    return rsaUtil.generateKeyPair()
  },

  /**
   * 验证密钥
   * @param {string} key - 密钥
   * @param {string} type - 类型
   * @returns {boolean} 是否有效
   */
  validateKey(key, type) {
    return rsaUtil.validateKey(key, type)
  }
}
