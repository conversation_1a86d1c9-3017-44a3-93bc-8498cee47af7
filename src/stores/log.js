import { defineStore } from 'pinia'
import { ref } from 'vue'
import { rsaCrypto } from '@/utils/crypto/rsa'

/**
 * 登录信息存储管理
 * 使用RSA加密存储敏感的登录信息
 */
export const useLogStore = defineStore('log', () => {
  // 状态
  const isInitialized = ref(false)
  const keyPair = ref(null)

  // 存储键名
  const STORAGE_KEYS = {
    PUBLIC_KEY: 'log_public_key',
    PRIVATE_KEY: 'log_private_key',
    ENCRYPTED_USERNAME: 'log_encrypted_username',
    ENCRYPTED_PASSWORD: 'log_encrypted_password',
    REMEMBER_ME: 'log_remember_me'
  }

  /**
   * 初始化RSA密钥对
   * 如果本地没有密钥对，则生成新的密钥对并保存
   */
  const initializeKeys = () => {
    try {
      // 检查本地是否已有密钥对
      const storedPublicKeyBase64 = localStorage.getItem(STORAGE_KEYS.PUBLIC_KEY)
      const storedPrivateKeyBase64 = localStorage.getItem(STORAGE_KEYS.PRIVATE_KEY)

      if (storedPublicKeyBase64 && storedPrivateKeyBase64) {
        try {
          // 解码Base64存储的密钥
          const storedPublicKey = atob(storedPublicKeyBase64)
          const storedPrivateKey = atob(storedPrivateKeyBase64)

          // 验证解码后的密钥是否有效
          if (rsaCrypto.validateKey(storedPublicKey, 'public') &&
              rsaCrypto.validateKey(storedPrivateKey, 'private')) {
            keyPair.value = {
              publicKey: storedPublicKey,
              privateKey: storedPrivateKey
            }

          } else {
            console.warn('存储的密钥无效，重新生成')
            generateNewKeyPair()
          }
        } catch (error) {
          console.warn('密钥解码失败，重新生成:', error)
          generateNewKeyPair()
        }
      } else {

        generateNewKeyPair()
      }

      isInitialized.value = true
    } catch (error) {
      console.error('初始化RSA密钥失败:', error)
      throw error
    }
  }

  /**
   * 生成新的RSA密钥对并保存到localStorage
   */
  const generateNewKeyPair = () => {
    try {
      const newKeyPair = rsaCrypto.generateKeyPair()
      
      // 将密钥转换为Base64格式保存
      const publicKeyBase64 = btoa(newKeyPair.publicKey)
      const privateKeyBase64 = btoa(newKeyPair.privateKey)

      // 保存到localStorage
      localStorage.setItem(STORAGE_KEYS.PUBLIC_KEY, publicKeyBase64)
      localStorage.setItem(STORAGE_KEYS.PRIVATE_KEY, privateKeyBase64)

      keyPair.value = {
        publicKey: newKeyPair.publicKey,
        privateKey: newKeyPair.privateKey
      }


    } catch (error) {
      console.error('生成RSA密钥对失败:', error)
      throw error
    }
  }

  /**
   * 保存加密的登录信息
   * @param {string} username - 用户名
   * @param {string} password - 密码
   */
  const saveLoginInfo = (username, password) => {
    try {
      if (!isInitialized.value || !keyPair.value) {
        initializeKeys()
      }

      // 加密用户名和密码
      const encryptedUsername = rsaCrypto.encryptWithPublicKey(username, keyPair.value.publicKey)
      const encryptedPassword = rsaCrypto.encryptWithPublicKey(password, keyPair.value.publicKey)

      // 保存加密后的数据
      localStorage.setItem(STORAGE_KEYS.ENCRYPTED_USERNAME, encryptedUsername)
      localStorage.setItem(STORAGE_KEYS.ENCRYPTED_PASSWORD, encryptedPassword)
      localStorage.setItem(STORAGE_KEYS.REMEMBER_ME, 'true')


    } catch (error) {
      console.error('保存登录信息失败:', error)
      throw error
    }
  }

  /**
   * 获取解密的登录信息
   * @returns {Object|null} 包含用户名和密码的对象，如果没有保存的信息则返回null
   */
  const getLoginInfo = () => {
    try {
      if (!isInitialized.value || !keyPair.value) {
        initializeKeys()
      }

      const rememberMe = localStorage.getItem(STORAGE_KEYS.REMEMBER_ME)
      if (rememberMe !== 'true') {
        return null
      }

      const encryptedUsername = localStorage.getItem(STORAGE_KEYS.ENCRYPTED_USERNAME)
      const encryptedPassword = localStorage.getItem(STORAGE_KEYS.ENCRYPTED_PASSWORD)

      if (!encryptedUsername || !encryptedPassword) {
        return null
      }

      // 解密用户名和密码
      const username = rsaCrypto.decryptWithPrivateKey(encryptedUsername, keyPair.value.privateKey)
      const password = rsaCrypto.decryptWithPrivateKey(encryptedPassword, keyPair.value.privateKey)

      return {
        username,
        password
      }
    } catch (error) {
      console.error('获取登录信息失败:', error)
      // 如果解密失败，清除可能损坏的数据
      clearLoginInfo()
      return null
    }
  }

  /**
   * 清除保存的登录信息
   */
  const clearLoginInfo = () => {
    try {
      localStorage.removeItem(STORAGE_KEYS.ENCRYPTED_USERNAME)
      localStorage.removeItem(STORAGE_KEYS.ENCRYPTED_PASSWORD)
      localStorage.removeItem(STORAGE_KEYS.REMEMBER_ME)

    } catch (error) {
      console.error('清除登录信息失败:', error)
    }
  }

  /**
   * 检查是否有保存的登录信息
   * @returns {boolean}
   */
  const hasLoginInfo = () => {
    const rememberMe = localStorage.getItem(STORAGE_KEYS.REMEMBER_ME)
    const encryptedUsername = localStorage.getItem(STORAGE_KEYS.ENCRYPTED_USERNAME)
    const encryptedPassword = localStorage.getItem(STORAGE_KEYS.ENCRYPTED_PASSWORD)
    
    return rememberMe === 'true' && !!encryptedUsername && !!encryptedPassword
  }

  /**
   * 清除所有数据（包括密钥）
   */
  const clearAllData = () => {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key)
      })
      keyPair.value = null
      isInitialized.value = false

    } catch (error) {
      console.error('清除所有数据失败:', error)
    }
  }

  /**
   * 获取密钥信息（用于调试）
   * @returns {Object|null}
   */
  const getKeyInfo = () => {
    if (!keyPair.value) {
      return null
    }
    
    return {
      publicKeyBase64: btoa(keyPair.value.publicKey),
      privateKeyBase64: btoa(keyPair.value.privateKey),
      hasKeys: !!keyPair.value.publicKey && !!keyPair.value.privateKey
    }
  }

  return {
    // 状态
    isInitialized,
    
    // 方法
    initializeKeys,
    saveLoginInfo,
    getLoginInfo,
    clearLoginInfo,
    hasLoginInfo,
    clearAllData,
    getKeyInfo
  }
})
