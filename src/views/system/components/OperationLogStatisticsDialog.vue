<template>
  <el-dialog
    v-model="dialogVisible"
    title="操作日志统计分析"
    :width="dialogWidth"
    :fullscreen="isMobile"
    :close-on-click-modal="false"
    append-to-body
    destroy-on-close
    class="statistics-dialog"
  >
    <div v-loading="loading" class="statistics-content">

      <!-- 基础统计信息 -->
      <div v-if="basicStats" class="stats-overview">
        <el-row :gutter="16">
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card primary">
              <div class="stat-number">{{ basicStats.totalCount }}</div>
              <div class="stat-label">总操作数</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card success">
              <div class="stat-number">{{ basicStats.successCount }}</div>
              <div class="stat-label">成功操作</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card danger">
              <div class="stat-number">{{ basicStats.failureCount }}</div>
              <div class="stat-label">失败操作</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card warning">
              <div class="stat-number">{{ basicStats.successRate }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="16" style="margin-top: 16px;">
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card info">
              <div class="stat-number">{{ basicStats.uniqueUsers }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </el-col>
          <el-col :xs="12" :sm="6" :md="6" :lg="6">
            <div class="stat-card secondary">
              <div class="stat-number">{{ basicStats.uniqueIps }}</div>
              <div class="stat-label">访问IP</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 统计图表 -->
      <div class="charts-section">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="模块统计" name="module">
            <div v-if="moduleStats" class="chart-container">
              <el-table :data="moduleStatsTable" stripe>
                <el-table-column prop="module" label="操作模块" />
                <el-table-column prop="count" label="操作次数" />
                <el-table-column prop="percentage" label="占比">
                  <template #default="{ row }">
                    <el-progress :percentage="row.percentage" :stroke-width="8" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="操作类型" name="type">
            <div v-if="typeStats" class="chart-container">
              <el-table :data="typeStatsTable" stripe>
                <el-table-column prop="type" label="操作类型" />
                <el-table-column prop="count" label="操作次数" />
                <el-table-column prop="percentage" label="占比">
                  <template #default="{ row }">
                    <el-progress :percentage="row.percentage" :stroke-width="8" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="用户统计" name="user">
            <div v-if="userStats" class="chart-container">
              <el-table :data="userStatsTable" stripe>
                <el-table-column prop="user" label="用户名" />
                <el-table-column prop="count" label="操作次数" />
                <el-table-column prop="percentage" label="占比">
                  <template #default="{ row }">
                    <el-progress :percentage="row.percentage" :stroke-width="8" />
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

import {
  getOperationLogStatistics,
  getModuleStatistics,
  getTypeStatistics,
  getUserStatistics,
  OPERATION_TYPE_LABELS
} from '@/api/operationLog'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const loading = ref(false)
const isMobile = ref(false)
const activeTab = ref('module')
const basicStats = ref(null)
const moduleStats = ref(null)
const typeStats = ref(null)
const userStats = ref(null)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogWidth = computed(() => {
  if (isMobile.value) {
    return '95%'
  } else if (window.innerWidth <= 1024) {
    return '90%'
  } else {
    return '1000px'
  }
})

// 默认查询参数（前7天）
const queryParams = computed(() => {
  return {} // 使用API默认的7天统计
})

const moduleStatsTable = computed(() => {
  if (!moduleStats.value) return []
  const total = Object.values(moduleStats.value).reduce((sum, count) => sum + count, 0)
  return Object.entries(moduleStats.value).map(([module, count]) => ({
    module,
    count,
    percentage: total > 0 ? Math.round((count / total) * 100) : 0
  })).sort((a, b) => b.count - a.count)
})

const typeStatsTable = computed(() => {
  if (!typeStats.value) return []
  const total = Object.values(typeStats.value).reduce((sum, count) => sum + count, 0)
  return Object.entries(typeStats.value).map(([type, count]) => ({
    type: OPERATION_TYPE_LABELS[type] || type,
    count,
    percentage: total > 0 ? Math.round((count / total) * 100) : 0
  })).sort((a, b) => b.count - a.count)
})

const userStatsTable = computed(() => {
  if (!userStats.value) return []
  const total = Object.values(userStats.value).reduce((sum, count) => sum + count, 0)
  return Object.entries(userStats.value).map(([user, count]) => ({
    user,
    count,
    percentage: total > 0 ? Math.round((count / total) * 100) : 0
  })).sort((a, b) => b.count - a.count)
})

// 方法
const loadStatistics = async () => {
  try {
    loading.value = true

    // 并行加载所有统计数据
    const [basicResponse, moduleResponse, typeResponse, userResponse] = await Promise.all([
      getOperationLogStatistics(queryParams.value),
      getModuleStatistics(queryParams.value),
      getTypeStatistics(queryParams.value),
      getUserStatistics({
        ...queryParams.value,
        limit: 20
      })
    ])

    // 设置所有统计数据
    basicStats.value = basicResponse
    moduleStats.value = moduleResponse
    typeStats.value = typeResponse
    userStats.value = userResponse

  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}



const handleClose = () => {
  dialogVisible.value = false
}

// 监听器
watch(() => props.visible, (newValue) => {
  if (newValue) {
    // 对话框打开时自动加载统计数据（默认前7天）
    loadStatistics()
  }
})

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.statistics-content {
  max-height: 70vh;
  overflow-y: auto;
  padding-top: 20px;
}

.stats-overview {
  margin-bottom: 30px;
}

.stat-card {
  background: var(--bg-color-container);
  border: 1px solid var(--border-color-lighter);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.success {
  border-color: var(--color-success);
}

.stat-card.danger {
  border-color: var(--color-danger);
}

.stat-card.warning {
  border-color: var(--color-warning);
}

.stat-card.info {
  border-color: var(--color-info);
}

.stat-card.primary {
  border-color: var(--color-primary);
}

.stat-card.secondary {
  border-color: #909399;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-color-primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-secondary);
}

.charts-section {
  margin-top: 20px;
}

.chart-container {
  min-height: 300px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .statistics-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .statistics-dialog :deep(.el-dialog__body) {
    padding: 16px;
  }
  
  .statistics-content {
    max-height: 60vh;
  }

  .stat-card {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .dialog-footer {
    padding: 16px;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
    height: 40px;
  }
}
</style>
