import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

/**
 * 主题管理状态管理
 * 统一管理应用的主题配置（浅色/深色主题）
 */
export const useThemeStore = defineStore('theme', () => {
  // 主题状态：'light' | 'dark'
  const currentTheme = ref('light')
  
  // 是否已初始化
  const isInitialized = ref(false)
  
  // 存储键名
  const STORAGE_KEY = 'app_theme'
  
  /**
   * 获取系统偏好的主题
   * @returns {string} 'light' | 'dark'
   */
  const getSystemTheme = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return 'light'
  }
  
  /**
   * 应用主题到DOM
   * @param {string} theme 主题名称
   */
  const applyTheme = (theme) => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      
      // 添加过渡效果
      root.style.transition = 'background-color 0.3s ease, color 0.3s ease'
      
      if (theme === 'dark') {
        root.setAttribute('data-theme', 'dark')
      } else {
        root.removeAttribute('data-theme')
      }
      
      // 移除过渡效果（避免影响其他动画）
      setTimeout(() => {
        root.style.transition = ''
      }, 300)
    }
  }
  
  /**
   * 设置主题
   * @param {string} theme 主题名称 'light' | 'dark'
   */
  const setTheme = (theme) => {
    if (theme !== 'light' && theme !== 'dark') {
      console.warn('Invalid theme:', theme)
      return
    }
    
    currentTheme.value = theme
    applyTheme(theme)
    
    // 保存到localStorage
    try {
      localStorage.setItem(STORAGE_KEY, theme)
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
    }
  }
  
  /**
   * 切换主题
   */
  const toggleTheme = () => {
    const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }
  
  /**
   * 初始化主题
   * 优先级：localStorage > 系统偏好 > 默认浅色
   */
  const initTheme = () => {
    if (isInitialized.value) return
    
    let theme = 'light'
    
    try {
      // 尝试从localStorage读取
      const savedTheme = localStorage.getItem(STORAGE_KEY)
      if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
        theme = savedTheme
      } else {
        // 如果没有保存的主题，使用系统偏好
        theme = getSystemTheme()
      }
    } catch (error) {
      console.warn('Failed to read theme from localStorage:', error)
      // 降级到系统偏好
      theme = getSystemTheme()
    }
    
    currentTheme.value = theme
    applyTheme(theme)
    isInitialized.value = true
  }
  
  /**
   * 监听系统主题变化
   */
  const watchSystemTheme = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = (e) => {
        // 只有在用户没有手动设置过主题时才跟随系统
        try {
          const savedTheme = localStorage.getItem(STORAGE_KEY)
          if (!savedTheme) {
            const systemTheme = e.matches ? 'dark' : 'light'
            setTheme(systemTheme)
          }
        } catch (error) {
          console.warn('Failed to handle system theme change:', error)
        }
      }
      
      // 添加监听器
      if (mediaQuery.addListener) {
        mediaQuery.addListener(handleChange)
      } else if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleChange)
      }
      
      // 返回清理函数
      return () => {
        if (mediaQuery.removeListener) {
          mediaQuery.removeListener(handleChange)
        } else if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener('change', handleChange)
        }
      }
    }
  }
  
  /**
   * 获取当前主题是否为深色
   * @returns {boolean}
   */
  const isDark = () => {
    return currentTheme.value === 'dark'
  }
  
  /**
   * 获取当前主题是否为浅色
   * @returns {boolean}
   */
  const isLight = () => {
    return currentTheme.value === 'light'
  }
  
  /**
   * 重置主题为系统偏好
   */
  const resetToSystem = () => {
    try {
      localStorage.removeItem(STORAGE_KEY)
      const systemTheme = getSystemTheme()
      setTheme(systemTheme)
    } catch (error) {
      console.warn('Failed to reset theme:', error)
    }
  }
  
  // 监听主题变化，确保DOM同步
  watch(currentTheme, (newTheme) => {
    applyTheme(newTheme)
  })
  
  return {
    // 状态
    currentTheme,
    isInitialized,
    
    // 计算属性方法
    isDark,
    isLight,
    
    // 操作方法
    setTheme,
    toggleTheme,
    initTheme,
    resetToSystem,
    watchSystemTheme,
    getSystemTheme
  }
})
