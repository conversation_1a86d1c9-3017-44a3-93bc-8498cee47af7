<template>
  <div class="management-page">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="用户账号">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入账号"
            clearable
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="操作模块">
          <el-input
            v-model="searchForm.operationModule"
            placeholder="请输入操作模块"
            clearable
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select
            v-model="searchForm.operationType"
            placeholder="请选择操作类型"
            clearable
            class="search-select"
          >
            <el-option
              v-for="(label, value) in OPERATION_TYPE_LABELS"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="操作状态">
          <el-select
            v-model="searchForm.operationStatus"
            placeholder="请选择状态"
            clearable
            class="search-select"
          >
            <el-option label="成功" :value="1" />
            <el-option label="失败" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="请求方法">
          <el-select
            v-model="searchForm.requestMethod"
            placeholder="请选择方法"
            clearable
            class="search-select"
          >
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" />
            <el-option label="PATCH" value="PATCH" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户端IP">
          <el-input
            v-model="searchForm.clientIp"
            placeholder="请输入IP地址"
            clearable
            class="search-input"
          />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            :editable="false"
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="date-range-picker"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <el-button type="info" @click="handleViewStatistics">
          <el-icon><DataAnalysis /></el-icon>
          统计分析
        </el-button>
        <el-button type="warning" @click="handleCleanup">
          <el-icon><Delete /></el-icon>
          清理
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{ background: 'var(--bg-color-table-header)', color: 'var(--text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '56px' }"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="username" label="用户名" min-width="120" align="center" header-align="center">
          <template #default="{ row }">
            <div class="user-info">
              <span class="username">{{ row.username }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="operationTime" label="操作时间" min-width="160" align="center" header-align="center" sortable="custom">
          <template #default="{ row }">
            {{ row.operationTime }}
          </template>
        </el-table-column>
        <el-table-column prop="operationModule" label="操作模块" min-width="120" align="center" header-align="center">
          <template #default="{ row }">
            <span class="operation-module">{{ row.operationModule }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="operationType" label="操作类型" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTagType(row.operationType)" size="small">
              {{ OPERATION_TYPE_LABELS[row.operationType] || row.operationType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operationDesc" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="operationStatus" label="状态" width="80" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="row.operationStatus === 1 ? 'success' : 'danger'" size="small">
              {{ OPERATION_STATUS_LABELS[row.operationStatus] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="requestMethod" label="请求方法" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="getRequestMethodTagType(row.requestMethod)" size="small">
              {{ row.requestMethod }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="clientIp" label="客户端IP" min-width="130" align="center" header-align="center">
          <template #default="{ row }">
            <span class="ip-address">{{ row.clientIp }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="地区" min-width="150" align="center" header-align="center">
          <template #default="{ row }">
            <span v-if="row.location">{{ row.location }}</span>
            <span v-else class="text-placeholder">未知</span>
          </template>
        </el-table-column>
        <el-table-column prop="executionTime" label="执行时间" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <span class="execution-time">{{ row.executionTime }}ms</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons-row">
              <el-tooltip content="查看详情" placement="top">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleViewDetail(row)"
                >
                  详情
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.size"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 操作日志详情对话框 -->
    <OperationLogDetailDialog
      v-model:visible="detailDialogVisible"
      :log-id="selectedLogId"
      @success="loadTableData"
    />

    <!-- 清理日志对话框 -->
    <CleanupOperationLogsDialog
      v-model:visible="cleanupDialogVisible"
      @success="loadTableData"
    />

    <!-- 统计分析对话框 -->
    <OperationLogStatisticsDialog
      v-model:visible="statisticsDialogVisible"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search, Refresh, Warning, Delete, DataAnalysis
} from '@element-plus/icons-vue'
import {
  getOperationLogPage,
  OPERATION_TYPE_LABELS, OPERATION_STATUS_LABELS
} from '@/api/operationLog'
import OperationLogDetailDialog from './components/OperationLogDetailDialog.vue'
import CleanupOperationLogsDialog from './components/CleanupOperationLogsDialog.vue'
import OperationLogStatisticsDialog from './components/OperationLogStatisticsDialog.vue'
import Pagination from '@/components/Pagination.vue'

// 页面标题
defineOptions({
  name: 'OperationLogManagement'
})

// 响应式数据
const tableLoading = ref(false)
const tableData = ref([])
const detailDialogVisible = ref(false)
const cleanupDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)
const selectedLogId = ref('')
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  operationModule: '',
  operationType: '',
  operationStatus: '',
  requestMethod: '',
  clientIp: '',
  startTime: '',
  endTime: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 排序数据
const sortData = reactive({
  prop: '',
  order: ''
})

// 计算属性
const searchParams = computed(() => {
  const params = {
    current: pagination.current,
    size: pagination.size,
    ...searchForm
  }
  
  // 移除空值
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key]
    }
  })
  
  return params
})

// 获取操作类型标签类型
const getOperationTypeTagType = (type) => {
  const typeMap = {
    CREATE: 'success',
    UPDATE: 'warning',
    DELETE: 'danger',
    QUERY: 'info',
    LOGIN: 'success',
    LOGOUT: 'info',
    EXPORT: 'warning',
    IMPORT: 'warning',
    APPROVE: 'success',
    REJECT: 'danger',
    ENABLE: 'success',
    DISABLE: 'warning',
    RESET: 'warning',
    OTHER: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取请求方法标签类型
const getRequestMethodTagType = (method) => {
  const typeMap = {
    GET: 'info',
    POST: 'success',
    PUT: 'warning',
    DELETE: 'danger',
    PATCH: 'warning'
  }
  return typeMap[method] || 'info'
}

// 方法
const loadTableData = async () => {
  try {
    tableLoading.value = true
    const response = await getOperationLogPage(searchParams.value)

    tableData.value = response.records || []
    pagination.total = response.total || 0
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('加载数据失败:', error)
  } finally {
    tableLoading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadTableData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  dateRange.value = []
  pagination.current = 1
  loadTableData()
}

const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    searchForm.startTime = dates[0]
    searchForm.endTime = dates[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadTableData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadTableData()
}

const handleSortChange = ({ prop, order }) => {
  sortData.prop = prop
  sortData.order = order
  loadTableData()
}

const handleViewDetail = (row) => {
  selectedLogId.value = row.id
  detailDialogVisible.value = true
}

const handleViewStatistics = () => {
  statisticsDialogVisible.value = true
}

const handleCleanup = () => {
  cleanupDialogVisible.value = true
}

// 生命周期
onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
/* 使用全局样式，这里只添加特定样式 */
.action-buttons {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-buttons-row {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

/* 表格内容样式 */
.user-info {
  display: flex;
  align-items: center;
  justify-content: center;
}

.username {
  font-weight: 500;
  color: var(--text-color-primary);
}

.operation-module {
  color: var(--text-color-primary);
}

.ip-address {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-color-secondary);
}

.execution-time {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-color-secondary);
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }

  .table-section {
    overflow-x: auto;
  }
}

/* 搜索表单控件样式 */
.search-input {
  width: 200px;
}

.search-select {
  width: 150px; /* 比登录日志管理页面稍宽，适应操作类型选项 */
}

/* 时间范围选择器响应式样式 */
.date-range-picker {
  width: 350px;
}

/* 移动端搜索表单适配 */
@media (max-width: 768px) {
  .search-input,
  .search-select,
  .date-range-picker {
    width: 100% !important;
  }
}
</style>
