<template>
  <div class="system-error-container">
    <div class="error-content">
      <!-- 图标 -->
      <div class="icon-container">
        <el-icon :size="120" :color="'var(--danger-color)'">
          <Warning />
        </el-icon>
      </div>
      
      <!-- 标题 -->
      <h1 class="title">系统异常</h1>
      <h2 class="subtitle">数据获取异常，请重试</h2>
      
      <!-- 描述 -->
      <p class="description">
        系统无法获取您的信息，可能是网络连接问题或服务器异常。<br>
        页面将在 <span class="countdown">{{ countdown }}</span> 秒后自动刷新。
      </p>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <el-button type="primary" @click="handleRefresh" :loading="refreshing">
          <el-icon><Refresh /></el-icon>
          立即刷新
        </el-button>
        <el-button @click="goToLogin">
          <el-icon><User /></el-icon>
          重新登录
        </el-button>
      </div>
      
      <!-- 错误详情（开发环境显示） -->
      <div class="error-details" v-if="showErrorDetails && errorMessage">
        <el-collapse>
          <el-collapse-item title="错误详情" name="error">
            <div class="error-message">
              <pre>{{ errorMessage }}</pre>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Warning, Refresh, User } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 倒计时
const countdown = ref(30)
const refreshing = ref(false)
let countdownTimer = null

// 是否显示错误详情（开发环境）
const showErrorDetails = ref(import.meta.env.DEV)

// 错误信息
const errorMessage = ref(route.query.error || '')

// 开始倒计时
const startCountdown = () => {
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      handleRefresh()
    }
  }, 1000)
}

// 手动刷新
const handleRefresh = () => {
  refreshing.value = true
  
  // 清除倒计时
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  
  // 跳转到根地址，触发权限重新获取
  window.location.href = '/'
}

// 重新登录
const goToLogin = () => {
  // 清除倒计时
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  
  // 退出登录并跳转到登录页
  authStore.logout(false)
  router.push('/login')
}

// 组件挂载时开始倒计时
onMounted(() => {
  startCountdown()
})

// 组件卸载时清除倒计时
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})
</script>

<style scoped>
.system-error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-color-page) 0%, var(--border-color-light) 100%);
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  background: var(--bg-color-card);
  border-radius: 12px;
  padding: 40px;
  box-shadow: var(--box-shadow-xl);
}

.icon-container {
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}


@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}


.title {
  font-size: 48px;
  font-weight: bold;
  color: var(--danger-color);
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 24px;
  color: var(--text-color-secondary);
  margin: 0 0 20px 0;
  font-weight: 500;
}

.description {
  font-size: 16px;
  color: var(--text-color-placeholder);
  line-height: 1.6;
  margin-bottom: 30px;
}

.countdown {
  color: var(--danger-color);
  font-weight: bold;
  font-size: 18px;
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.error-details {
  margin-top: 20px;
  text-align: left;
}

.error-message {
  background: var(--bg-color-page);
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-color-secondary);
  max-height: 200px;
  overflow-y: auto;
}

.error-message pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-content {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 36px;
  }
  
  .subtitle {
    font-size: 20px;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .actions .el-button {
    width: 200px;
  }
}
</style>
