import { defineStore } from 'pinia'
import { ref } from 'vue'
import { menuApi } from '@/api'

export const useMenuStore = defineStore('menu', () => {
  // 状态
  const menus = ref([])
  const loading = ref(false)
  const isLoaded = ref(false) // 菜单是否已加载
  const routesAdded = ref(false) // 动态路由是否已添加

  // 缓存正在进行的菜单获取请求，避免重复请求
  let fetchPromise = null

  /**
   * 处理菜单数据，统一处理外部链接的路径映射
   * @param {Array} menuList 原始菜单数据
   * @returns {Array} 处理后的菜单数据
   */
  const processMenuData = (menuList) => {
    return menuList.map(menu => {
      // 处理当前菜单项
      const processedMenu = { ...menu }

      // 如果是外部链接，添加原始外链地址字段，并处理路径映射
      if (menu.externalLink === 1) {
        // 保存原始外链地址
        processedMenu.originalExternalUrl = menu.routePath

        // 如果是当前窗口打开，修改 routePath 为生成的路由路径
        if (menu.openMode === '_self') {
          processedMenu.routePath = `/link/${menu.id}`
        }
        // 新窗口打开的保持原路径不变
      }

      // 递归处理子菜单
      if (menu.children && menu.children.length > 0) {
        processedMenu.children = processMenuData(menu.children)
      }

      return processedMenu
    })
  }

  /**
   * 获取用户菜单列表
   * @returns {Promise<Array<Object>>}
   */
  const fetchMenus = async () => {
    // 如果已经在加载中，返回正在进行的请求
    if (fetchPromise) {
      return fetchPromise
    }

    // 如果已经加载过，直接返回
    if (isLoaded.value) {
      return menus.value
    }

    // 创建新的请求
    fetchPromise = (async () => {
      try {
        loading.value = true
        const response = await menuApi.getUserMenus()
        // 处理菜单数据，统一处理外部链接的路径映射
        menus.value = processMenuData(response || [])
        isLoaded.value = true
        return menus.value
      } catch (error) {
        console.error('获取用户菜单失败:', error)
        // 不显示错误消息，由路由守卫统一处理
        menus.value = []
        isLoaded.value = false
        throw error
      } finally {
        loading.value = false
        fetchPromise = null // 清除请求缓存
      }
    })()

    return fetchPromise
  }

  /**
   * 根据路径查找菜单项
   * @param {string} path 路由路径
   * @param {Array} menuList 菜单列表，默认使用当前菜单
   * @returns {Object|null} 找到的菜单项
   */
  const findMenuByPath = (path, menuList = menus.value) => {
    for (const menu of menuList) {
      if (menu.routePath === path) {
        return menu
      }
      if (menu.children && menu.children.length > 0) {
        const found = findMenuByPath(path, menu.children)
        if (found) {
          return found
        }
      }
    }
    return null
  }



  /**
   * 根据菜单编码查找菜单项
   * @param {string} menuCode 菜单编码
   * @param {Array} menuList 菜单列表，默认使用当前菜单
   * @returns {Object|null} 找到的菜单项
   */
  const findMenuByCode = (menuCode, menuList = menus.value) => {
    for (const menu of menuList) {
      if (menu.menuCode === menuCode) {
        return menu
      }
      if (menu.children && menu.children.length > 0) {
        const found = findMenuByCode(menuCode, menu.children)
        if (found) {
          return found
        }
      }
    }
    return null
  }

  /**
   * 获取扁平化的菜单列表（包含所有层级）
   * @param {Array} menuList 菜单列表，默认使用当前菜单
   * @returns {Array<Object>} 扁平化的菜单列表
   */
  const getFlatMenus = (menuList = menus.value) => {
    const flatMenus = []
    
    const flatten = (items) => {
      for (const item of items) {
        flatMenus.push(item)
        if (item.children && item.children.length > 0) {
          flatten(item.children)
        }
      }
    }
    
    flatten(menuList)
    return flatMenus
  }

  /**
   * 获取可见的菜单列表（过滤隐藏菜单）
   * @param {Array} menuList 菜单列表，默认使用当前菜单
   * @returns {Array<Object>} 可见的菜单列表
   */
  const getVisibleMenus = (menuList = menus.value) => {
    return menuList.filter(menu => {
      if (menu.visible === 0) {
        return false
      }
      if (menu.children && menu.children.length > 0) {
        menu.children = getVisibleMenus(menu.children)
      }
      return true
    })
  }

  /**
   * 清除菜单数据
   */
  const clearMenus = () => {
    menus.value = []
    isLoaded.value = false
    routesAdded.value = false
    fetchPromise = null // 清除请求缓存
  }

  /**
   * 标记动态路由已添加
   */
  const markRoutesAdded = () => {
    routesAdded.value = true
  }

  /**
   * 设置菜单数据（用于测试或其他场景）
   * @param {Array<Object>} newMenus 新的菜单数组
   */
  const setMenus = (newMenus) => {
    menus.value = newMenus || []
    isLoaded.value = true // 设置菜单后标记为已加载
  }

  return {
    // 状态
    menus,
    loading,
    isLoaded,
    routesAdded,

    // 方法
    fetchMenus,
    findMenuByPath,
    findMenuByCode,
    getFlatMenus,
    getVisibleMenus,
    clearMenus,
    setMenus,
    markRoutesAdded
  }
})
