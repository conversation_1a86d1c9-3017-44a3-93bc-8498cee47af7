/* Element Plus MessageBox 重构样式 - 简洁谷歌风格 */

/* MessageBox 遮罩层样式 */
.el-overlay.is-message-box {
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

/* MessageBox 主容器样式 */
.el-message-box {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  border: none !important;
  background: var(--bg-color) !important;
  overflow: hidden !important;
  min-width: 320px !important;
  max-width: 400px !important;

  /* 响应式适配 */
  @media (max-width: 768px) {
    min-width: 280px !important;
    width: 85vw !important;
    margin: 20px !important;
  }
}
.el-overlay.is-message-box .el-overlay-message-box {
  padding: 0px !important;
}
/* MessageBox 头部样式 */
.el-message-box__header {
  padding: 20px 20px 0 20px !important;
  border-bottom: none !important;
  background: var(--bg-color) !important;

  .el-message-box__title {
    font-size: 16px !important;
    font-weight: 500 !important;
    color: var(--text-color-primary) !important;
    line-height: 1.5 !important;
    margin: 0 !important;
  }
}

/* MessageBox 关闭按钮 */
.el-message-box__headerbtn {
  top: 12px !important;
  right: 12px !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  background: transparent !important;
  border: none !important;
  transition: background-color 0.2s ease !important;

  &:hover {
    background: rgba(0, 0, 0, 0.04) !important;
  }

  .el-message-box__close {
    font-size: 14px !important;
    color: var(--text-color-regular) !important;
    font-weight: normal !important;

    &:hover {
      color: var(--text-color-primary) !important;
    }
  }
}

/* MessageBox 内容区域 */
.el-message-box__content {
  padding: 20px !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;

  .el-message-box__status {
    flex-shrink: 0 !important;
    font-size: 20px !important;
    margin-top: 1px !important;

    /* 不同状态的图标颜色 */
    &.el-icon-success {
      color: #4caf50 !important;
    }

    &.el-icon-warning {
      color: #ff9800 !important;
    }

    &.el-icon-error {
      color: #f44336 !important;
    }

    &.el-icon-info {
      color: #2196f3 !important;
    }
  }

  .el-message-box__message {
    flex: 1 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: var(--text-color-primary) !important;
    margin: 0 !important;

    p {
      margin: 0 !important;
    }
  }

  /* 输入框样式 */
  .el-message-box__input {
    margin-top: 16px !important;
    width: 100% !important;

    .el-input__wrapper {
      border-radius: 4px !important;
      border: 1px solid var(--border-color) !important;
      transition: border-color 0.2s ease !important;

      &:hover {
        border-color: var(--primary-color) !important;
      }

      &.is-focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 1px var(--primary-color) !important;
      }
    }
  }
}

/* MessageBox 按钮区域 */
.el-message-box__btns {
  padding: 8px 20px 20px 20px !important;
  border-top: none !important;
  background: var(--bg-color) !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 8px !important;

  .el-button {
    min-width: 64px !important;
    height: 36px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.2s ease !important;

    /* 取消按钮样式 - 文本按钮 */
    &:not(.el-button--primary):not(.el-button--danger) {
      background: transparent !important;
      border: none !important;
      color: var(--primary-color) !important;
      padding: 0 16px !important;

      &:hover {
        background: rgba(59, 130, 246, 0.04) !important;
      }

      &:active {
        background: rgba(59, 130, 246, 0.08) !important;
      }
    }

    /* 主要按钮样式 - 填充按钮 */
    &.el-button--primary {
      background: var(--primary-color) !important;
      border: none !important;
      color: white !important;
      padding: 0 24px !important;

      &:hover {
        background: var(--primary-light) !important;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2) !important;
      }

      &:active {
        background: var(--primary-dark) !important;
        box-shadow: none !important;
      }
    }

    /* 危险按钮样式 */
    &.el-button--danger {
      background: #f44336 !important;
      border: none !important;
      color: white !important;
      padding: 0 24px !important;

      &:hover {
        background: #f66 !important;
        box-shadow: 0 2px 4px rgba(244, 67, 54, 0.2) !important;
      }

      &:active {
        background: #d32f2f !important;
        box-shadow: none !important;
      }
    }
  }

  /* 移动端按钮布局 */
  @media (max-width: 768px) {
    padding: 8px 16px 16px 16px !important;

    .el-button {
      min-width: 80px !important;
    }
  }
}

/* MessageBox 深色模式适配 */
[data-theme="dark"] {
  .el-message-box {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
  }

  .el-message-box__headerbtn:hover {
    background: rgba(255, 255, 255, 0.08) !important;
  }

  .el-message-box__btns .el-button:not(.el-button--primary):not(.el-button--danger):hover {
    background: rgba(59, 130, 246, 0.08) !important;
  }
}
