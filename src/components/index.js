// 全局组件注册文件
import SvgIcon from './SvgIcon.vue'
import ThemeToggle from './ThemeToggle.vue'
import TagsView from './TagsView.vue'
import Pagination from './Pagination.vue'

// 导出所有全局组件
export {
  SvgIcon,
  ThemeToggle,
  TagsView,
  Pagination
}

// 全局注册组件的安装函数
export default {
  install(app) {
    // 注册 SvgIcon 组件
    app.component('SvgIcon', SvgIcon)
    app.component('svg-icon', SvgIcon) // 支持 kebab-case 命名

    // 注册 ThemeToggle 组件
    app.component('ThemeToggle', ThemeToggle)
    app.component('theme-toggle', ThemeToggle) // 支持 kebab-case 命名

    // 注册 TagsView 组件
    app.component('TagsView', TagsView)
    app.component('tags-view', TagsView) // 支持 kebab-case 命名

    // 注册 Pagination 组件
    app.component('Pagination', Pagination)
    app.component('app-pagination', Pagination) // 支持 kebab-case 命名
  }
}
