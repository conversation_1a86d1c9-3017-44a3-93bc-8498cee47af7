/**
 * 组件路径映射配置
 * 统一管理所有页面组件的路径和描述
 */

export const componentConfig = {
  // 系统管理模块
  '@/views/system/MenuManagement.vue': {
    import: () => import('@/views/system/MenuManagement.vue'),
    description: '菜单管理页面'
  },
  '@/views/system/UserTypeManagement.vue': {
    import: () => import('@/views/system/UserTypeManagement.vue'),
    description: '用户类型管理页面'
  },
  '@/views/system/UserManagement.vue': {
    import: () => import('@/views/system/UserManagement.vue'),
    description: '用户管理页面'
  },
  '@/views/system/LoginLogManagement.vue': {
    import: () => import('@/views/system/LoginLogManagement.vue'),
    description: '登录日志管理页面'
  },
  '@/views/system/LoginLogAnalytics.vue': {
    import: () => import('@/views/system/LoginLogAnalytics.vue'),
    description: '登录日志统计分析页面'
  },
  '@/views/system/OperationLogManagement.vue': {
    import: () => import('@/views/system/OperationLogManagement.vue'),
    description: '操作日志管理页面'
  },

  // 错误页面
  '@/views/403/Forbidden.vue': {
    import: () => import('@/views/403/Forbidden.vue'),
    description: '403禁止访问页面'
  },
  '@/views/404/NotFound.vue': {
    import: () => import('@/views/404/NotFound.vue'),
    description: '404页面未找到'
  },
  '@/views/error/SystemError.vue': {
    import: () => import('@/views/error/SystemError.vue'),
    description: '系统错误页面'
  },

  // 认证页面
  '@/views/auth/SessionExpired.vue': {
    import: () => import('@/views/auth/SessionExpired.vue'),
    description: '会话过期页面'
  },

  // 其他页面
  '@/views/Login.vue': {
    import: () => import('@/views/Login.vue'),
    description: '登录页面'
  },
  '@/views/Redirect.vue': {
    import: () => import('@/views/Redirect.vue'),
    description: '重定向页面'
  },
  '@/views/Dashboard.vue': {
    import: () => import('@/views/Dashboard.vue'),
    description: '仪表盘页面'
  },
  '@/views/IframeView.vue': {
    import: () => import('@/views/IframeView.vue'),
    description: '外部链接页面'
  }
}

/**
 * 获取组件导入函数
 * @param {string} path 组件路径
 * @returns {Function} 组件导入函数
 */
export const getComponentImport = (path) => {
  return componentConfig[path]?.import
}

/**
 * 获取组件描述
 * @param {string} path 组件路径
 * @returns {string} 组件描述
 */
export const getComponentDescription = (path) => {
  return componentConfig[path]?.description || '自定义组件'
}

/**
 * 获取所有可用的组件路径
 * @returns {Array} 组件路径列表
 */
export const getAvailableComponents = () => {
  return Object.keys(componentConfig).sort()
}

/**
 * 获取所有组件及其描述
 * @returns {Object} 组件路径和描述的映射
 */
export const getAllComponentsWithDescriptions = () => {
  const result = {}
  Object.entries(componentConfig).forEach(([path, config]) => {
    result[path] = config.description
  })
  return result
}
