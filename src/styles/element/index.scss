// Element Plus 主题定制文件
// 使用 @forward 覆盖 Element Plus 默认变量

/* 覆盖 Element Plus 默认颜色变量 */
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // 主色调配置 - 使用项目统一的蓝色主题
  $colors: (
    'primary': (
      'base': #3B82F6,
      'light-3': #60A5FA,
      'light-5': #93C5FD,
      'light-7': #BFDBFE,
      'light-8': #DBEAFE,
      'light-9': #EFF6FF,
      'dark-2': #2563EB,
    ),
    'success': (
      'base': #10B981,
      'light-3': #34D399,
      'light-5': #6EE7B7,
      'light-7': #A7F3D0,
      'light-8': #D1FAE5,
      'light-9': #ECFDF5,
      'dark-2': #059669,
    ),
    'warning': (
      'base': #F59E0B,
      'light-3': #FBBF24,
      'light-5': #FCD34D,
      'light-7': #FDE68A,
      'light-8': #FEF3C7,
      'light-9': #FFFBEB,
      'dark-2': #D97706,
    ),
    'danger': (
      'base': #EF4444,
      'light-3': #F87171,
      'light-5': #FCA5A5,
      'light-7': #FECACA,
      'light-8': #FEE2E2,
      'light-9': #FEF2F2,
      'dark-2': #DC2626,
    ),
    'error': (
      'base': #EF4444,
      'light-3': #F87171,
      'light-5': #FCA5A5,
      'light-7': #FECACA,
      'light-8': #FEE2E2,
      'light-9': #FEF2F2,
      'dark-2': #DC2626,
    ),
    'info': (
      'base': #6B7280,
      'light-3': #9CA3AF,
      'light-5': #D1D5DB,
      'light-7': #E5E7EB,
      'light-8': #F3F4F6,
      'light-9': #F9FAFB,
      'dark-2': #4B5563,
    )
  ),
  
  // 文本颜色配置
  $text-color: (
    'primary': #1F2937,
    'regular': #374151,
    'secondary': #6B7280,
    'placeholder': #9CA3AF,
    'disabled': #D1D5DB,
  ),
  
  // 边框颜色配置
  $border-color: (
    '': #E5E7EB,
    'light': #F3F4F6,
    'lighter': #F9FAFB,
    'extra-light': #FAFBFC,
    'dark': #D1D5DB,
    'darker': #9CA3AF,
  ),
  
  // 填充颜色配置
  $fill-color: (
    '': #F3F4F6,
    'light': #F9FAFB,
    'lighter': #FAFBFC,
    'extra-light': #FEFEFE,
    'dark': #E5E7EB,
    'darker': #D1D5DB,
    'blank': transparent,
  ),
  
  // 背景颜色配置
  $bg-color: (
    '': #FFFFFF,
    'page': #F8FAFC,
    'overlay': #FFFFFF,
  ),
  
  // 圆角配置
  $border-radius: (
    'base': 6px,
    'small': 4px,
    'round': 20px,
    'circle': 100%,
  ),
  
  // 阴影配置
  $box-shadow: (
    '': (0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04)),
    'light': (0 2px 12px 0 rgba(0, 0, 0, 0.1)),
    'base': (0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04)),
    'dark': (0 4px 8px rgba(0, 0, 0, 0.12), 0 0 8px rgba(0, 0, 0, 0.04)),
  ),
  
  // 字体大小配置
  $font-size: (
    'extra-large': 20px,
    'large': 18px,
    'medium': 16px,
    'base': 14px,
    'small': 13px,
    'extra-small': 12px,
  ),
  
  // 组件尺寸配置
  $common-component-size: (
    'large': 40px,
    'default': 32px,
    'small': 24px,
  ),
);

// 导入所有 Element Plus 样式
@use "element-plus/theme-chalk/src/index.scss" as *;
